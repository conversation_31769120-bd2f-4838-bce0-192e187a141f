# Alarm Processor Helm 配置文件
# 用于部署告警处理作业

alarm-processor:
  enabled: true
  
  # 作业配置
  job:
    name: "alarm-processor"
    className: "com.geeksec.alarmprocessor.job.AlarmProcessorJob"
    parallelism: 4
    
  # 镜像配置
  image:
    repository: "nta/alarm-processor"
    tag: "3.0.0"
    pullPolicy: "IfNotPresent"
    
  # 资源配置
  resources:
    requests:
      memory: "1Gi"
      cpu: "500m"
    limits:
      memory: "2Gi"
      cpu: "1000m"
      
  # 环境变量
  env:
    - name: "JAVA_OPTS"
      value: "-Xmx1536m -Xms512m -XX:+UseG1GC"
    - name: "FLINK_CONF_DIR"
      value: "/opt/flink/conf"
      
  # 配置文件
  config:
    # 作业基础配置
    job:
      name: "alarm-processor"
      parallelism: 4
      checkpointInterval: 60000
      restartAttempts: 3
      restartDelay: 10000
      
    # Kafka 配置
    kafka:
      bootstrapServers: "kafka-cluster:9092"
      groupId: "alarm-processor"
      input:
        topic: "alarm-events"
        startingOffsets: "latest"
        autoCommit: true
        commitInterval: 5000
      output:
        topic: "processed-alarms"
        batchSize: 100
        lingerMs: 1000
        
    # 处理配置
    processing:
      # 去重配置
      deduplication:
        enabled: true
        mode: "TIME_WINDOW"
        timeWindowMs: 60000
        maxCacheSize: 10000
        cacheExpirationMs: 300000
        
      # 格式化配置
      formatting:
        enabled: true
        includeReasonAnalysis: true
        includeHandlingSuggestions: true
        cacheEnabled: true
        maxCacheSize: 5000
        
      # 攻击链分析配置
      attackChain:
        enabled: true
        correlationWindowMs: 300000
        maxCacheSize: 15000
        minEventsForChain: 2
        
      # 批量处理配置
      batch:
        enabled: true
        maxBatchSize: 50
        maxWaitTimeMs: 30000
        checkIntervalMs: 5000
        
    # 输出配置
    output:
      # Doris 配置
      doris:
        enabled: true
        feNodes: "doris-fe-service:8030"
        database: "nta"
        table: "ods_alarm_log"
        username: "root"
        password: ""
        batchSize: 200
        batchInterval: 10000
        labelPrefix: "alarm-processor"
        
      # Kafka 通知配置
      notification:
        enabled: true
        topic: "alarm-notifications"
        batchSize: 50
        lingerMs: 1000
        
      # Elasticsearch 配置（可选）
      elasticsearch:
        enabled: false
        hosts: "elasticsearch-service:9200"
        index: "nta-alarms"
        batchSize: 100
        
    # 监控配置
    monitoring:
      enabled: true
      metricsInterval: 30000
      performanceLogging: true
      detailedMetrics: false
      
    # 白名单配置
    whitelist:
      enabled: false
      configPath: "alarm_whitelist.json"
      updateInterval: 300
      
  # 服务配置
  service:
    type: ClusterIP
    ports:
      - name: web
        port: 8081
        targetPort: 8081
      - name: rpc
        port: 6123
        targetPort: 6123
        
  # 健康检查
  healthCheck:
    enabled: true
    initialDelaySeconds: 60
    periodSeconds: 30
    timeoutSeconds: 10
    failureThreshold: 3
    
  # 持久化存储
  persistence:
    enabled: true
    storageClass: "standard"
    size: "10Gi"
    mountPath: "/opt/flink/checkpoints"
    
  # 网络策略
  networkPolicy:
    enabled: false
    
  # Pod 安全策略
  podSecurityPolicy:
    enabled: false
    
  # 服务账户
  serviceAccount:
    create: true
    name: "alarm-processor"
    
  # RBAC
  rbac:
    create: true
    
  # 节点选择器
  nodeSelector: {}
  
  # 容忍度
  tolerations: []
  
  # 亲和性
  affinity: {}
  
  # Pod 注解
  podAnnotations:
    prometheus.io/scrape: "true"
    prometheus.io/port: "8081"
    prometheus.io/path: "/metrics"
    
  # Pod 标签
  podLabels:
    app: "alarm-processor"
    component: "flink-job"
    version: "3.0.0"
    
  # 安全上下文
  securityContext:
    runAsNonRoot: true
    runAsUser: 9999
    fsGroup: 9999
    
  # 日志配置
  logging:
    level:
      com.geeksec.alarmprocessor: "INFO"
      org.apache.flink: "INFO"
      org.apache.kafka: "WARN"
      org.apache.doris: "WARN"
    pattern:
      console: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n"
      
  # 自动扩缩容
  autoscaling:
    enabled: false
    minReplicas: 1
    maxReplicas: 5
    targetCPUUtilizationPercentage: 70
    targetMemoryUtilizationPercentage: 80
