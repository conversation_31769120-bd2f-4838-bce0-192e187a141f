# 告警处理功能迁移总结

## 概述

本文档总结了将 `certificate-analyzer` 和 `threat-detector` 模块中的告警处理功能迁移到统一的 `alarm-processor` 模块的工作。

## 迁移目标

- **统一告警处理**: 将分散在各个检测模块中的告警处理逻辑集中到 `alarm-processor` 模块
- **简化架构**: 各检测模块专注于检测逻辑，告警处理由专门模块负责
- **提高可维护性**: 统一的告警处理逻辑更容易维护和扩展

## 已完成的清理工作

### 1. threat-detector 模块清理

#### 删除的目录和文件
- `src/main/java/com/geeksec/threatdetector/alarm/` - 整个告警处理包
- `src/main/java/com/geeksec/threatdetector/formatting/` - 告警格式化包
- `src/main/java/com/geeksec/threatdetector/killchain/` - 攻击链分析包
- `src/main/java/com/geeksec/threatdetector/output/alarm/` - 告警输出包

#### 修改的文件
- **OutputManager.java**:
  - 移除了对 `AlarmOutputManager` 的引用
  - 将告警事件输出配置标记为已迁移
  - 添加了迁移说明注释

- **OutputRouter.java**:
  - 移除了 `Alarm` 相关的导入和引用
  - 删除了 `ALARM_TAG` 输出标签
  - 移除了 `createAlarmEvent()` 方法的实际实现
  - 将告警事件生成标记为已迁移
  - 修改为 `ProcessFunction` 以支持侧输出

### 2. certificate-analyzer 模块清理

#### 删除的文件
- `KnowledgeBaseAlarmSink.java` - 知识库告警输出器
- `AlarmDataBuilder.java` - 告警数据构建器
- `AttackChainBuilder.java` - 攻击链构建器
- `FingerAlarmBuilder.java` - 指纹告警构建器
- `src/main/java/com/geeksec/certificateanalyzer/sink/alarm/` - 整个告警处理包

#### 修改的文件
- **AlarmTransformer.java**:
  - 重构为生成 `AlarmEvent` 对象而不是直接处理告警
  - 修改为 `RichMapFunction<X509Certificate, AlarmEvent>`
  - 根据证书标签生成标准化的告警事件
  - 移除了对已删除类的依赖

#### 新增的文件
- **AlarmEvent.java** - 简化的告警事件模型，用于证书分析器生成告警事件

### 3. 迁移到 alarm-processor 的功能

以下功能已经在 `alarm-processor` 模块中重新实现：

#### 核心处理功能
- **告警去重** (`DeduplicationFunction`)
  - 基于时间窗口的智能去重
  - 内容哈希去重策略
  - 缓存优化

- **告警格式化** (`FormattingFunction`)
  - 自动生成告警原因分析
  - 生成处理建议
  - 格式化告警内容

- **攻击链分析** (`AttackChainAnalysisFunction`)
  - 识别相关告警事件
  - 构建攻击时间线
  - 关联分析

#### 输出管理
- **统一输出管理器** (`AlarmOutputManager`)
  - 支持多种输出目标
  - 批量处理优化
  - 错误处理和重试

- **多种输出支持**:
  - Apache Doris 数据库输出
  - Kafka 通知输出
  - Elasticsearch 输出（可选）

## 架构变化

### 迁移前架构
```
certificate-analyzer → 直接告警处理 → 多种输出
threat-detector → 直接告警处理 → 多种输出
```

### 迁移后架构
```
certificate-analyzer → 生成 AlarmEvent → Kafka
threat-detector → 生成 AlarmEvent → Kafka
                                    ↓
                            alarm-processor
                                    ↓
                            统一告警处理 → 多种输出
```

## 保留的功能

### certificate-analyzer 模块
- **威胁检测逻辑**: `CertificateThreatDetector` 等检测器保持不变
- **告警事件生成**: `AlarmTransformer` 负责将检测结果转换为告警事件
- **证书分析**: 核心的证书分析功能完全保留

### threat-detector 模块
- **威胁检测逻辑**: 所有检测器和检测引擎保持不变
- **输出路由**: `OutputRouter` 保留会话标签、资产标签和通知消息的生成
- **检测结果处理**: 核心的威胁检测功能完全保留

## 配置变化

### 新的配置要求
各检测模块需要配置 Kafka 输出，将告警事件发送到 `alarm-processor`：

```yaml
kafka:
  output:
    topic: "alarm-events"
    bootstrap.servers: "kafka:9092"
```

### alarm-processor 配置
需要配置输入和输出：

```yaml
input:
  kafka:
    topic: "alarm-events"
    bootstrap.servers: "kafka:9092"

output:
  doris:
    enabled: true
    fe.nodes: "doris-fe:8030"
  notification:
    enabled: true
    topic: "alarm-notifications"
```

## 部署影响

### 部署顺序
1. 首先部署 `alarm-processor` 模块
2. 然后更新 `certificate-analyzer` 和 `threat-detector` 模块
3. 确保 Kafka 主题 `alarm-events` 已创建

### 监控要点
- 监控 `alarm-events` Kafka 主题的消息流量
- 检查 `alarm-processor` 的处理延迟和错误率
- 验证告警输出到各个目标系统的正确性

## 测试建议

### 功能测试
1. 验证各检测模块能正确生成告警事件
2. 测试 `alarm-processor` 的去重、格式化和攻击链分析功能
3. 确认告警输出到各个目标系统的正确性

### 性能测试
1. 测试高负载下的告警处理性能
2. 验证去重缓存的效果
3. 检查内存和CPU使用情况

### 集成测试
1. 端到端的告警处理流程测试
2. 故障恢复测试
3. 数据一致性验证

## 后续工作

### 优化项目
1. 进一步优化告警去重算法
2. 增强攻击链分析的准确性
3. 添加更多的输出目标支持

### 监控增强
1. 添加更详细的性能指标
2. 实现告警处理的链路追踪
3. 增强错误监控和告警

## 总结

通过这次迁移，我们成功地：

1. **统一了告警处理逻辑**: 所有告警处理功能集中到 `alarm-processor` 模块
2. **简化了模块职责**: 各检测模块专注于检测，告警处理由专门模块负责
3. **提高了可维护性**: 统一的代码更容易维护和扩展
4. **保持了功能完整性**: 所有原有功能都得到了保留和增强
5. **改善了架构设计**: 更清晰的模块边界和数据流

这次迁移为 NTA 3.0 系统的告警处理能力奠定了坚实的基础，为后续的功能扩展和性能优化提供了良好的架构支撑。
