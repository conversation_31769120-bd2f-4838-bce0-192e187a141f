# Alarm Processor - 告警处理作业

## 概述

Alarm Processor 是 NTA 3.0 系统中的统一告警处理作业，负责接收来自各个检测模块的告警事件，进行统一的处理和分析，然后输出到各种存储和通知系统。

## 架构设计

### 数据流架构

```
告警事件源 (Kafka) 
    ↓
告警事件验证和反序列化
    ↓
告警处理流水线
    ├── 事件转换
    ├── 告警去重
    ├── 告警格式化
    └── 攻击链分析
    ↓
告警分流处理
    ├── 高优先级告警
    ├── 普通告警
    └── 证书告警
    ↓
多种输出
    ├── Doris 数据库
    ├── Kafka 通知
    └── Elasticsearch (可选)
```

### 核心组件

1. **AlarmEventSourceFactory**: 告警事件数据源工厂
2. **AlarmProcessingPipeline**: 告警处理流水线
3. **AlarmOutputManager**: 告警输出管理器
4. **AlarmProcessorConfig**: 配置管理

## 功能特性

### 1. 统一告警处理
- 接收来自 certificate-analyzer、threat-detector 等模块的告警事件
- 标准化告警格式和处理流程
- 支持多种告警类型和级别

### 2. 智能去重
- 基于时间窗口的告警去重
- 支持内容哈希去重
- 可配置的去重策略和缓存大小

### 3. 告警格式化
- 自动生成告警原因分析
- 提供处理建议和应急响应指导
- 计算风险评分和置信度

### 4. 攻击链分析
- 关联相关告警事件
- 识别攻击时间线
- 计算攻击链相关性分数

### 5. 多种输出支持
- **Doris**: 主要的告警数据存储
- **Kafka**: 实时告警通知
- **Elasticsearch**: 可选的搜索和分析

### 6. 高可用性
- 支持 Flink 检查点和状态恢复
- 自动重启和故障恢复
- 监控指标和性能统计

## 配置说明

### 主要配置参数

```yaml
alarm:
  processor:
    # 作业配置
    job:
      name: "alarm-processor"
      parallelism: 4
      checkpointInterval: 60000
    
    # Kafka 配置
    kafka:
      bootstrapServers: "kafka:9092"
      groupId: "alarm-processor"
      input:
        topic: "alarm-events"
      output:
        topic: "processed-alarms"
    
    # 处理配置
    processing:
      deduplication:
        enabled: true
        timeWindowMs: 60000
      formatting:
        enabled: true
        includeReasonAnalysis: true
      attackChain:
        enabled: true
        correlationWindowMs: 300000
    
    # 输出配置
    output:
      doris:
        enabled: true
        feNodes: "doris-fe:8030"
        database: "nta"
        table: "ods_alarm_log"
```

## 部署指南

### 1. 构建项目

```bash
cd flink-jobs/alarm-processor
mvn clean package
```

### 2. Docker 部署

```bash
# 构建镜像
docker build -t nta/alarm-processor:latest .

# 运行容器
docker run -d \
  --name alarm-processor \
  --network nta-network \
  -v /path/to/config:/opt/flink/conf \
  nta/alarm-processor:latest \
  standalone-job \
  --job-classname com.geeksec.alarmprocessor.job.AlarmProcessorJob
```

### 3. Kubernetes 部署

```bash
# 使用 Helm 部署
helm install alarm-processor ./deployment/helm/alarm-processor \
  --namespace nta \
  --values values-prod.yaml
```

## 监控和运维

### 关键指标

- **告警处理速度**: 每秒处理的告警数量
- **去重效率**: 去重缓存命中率
- **输出成功率**: 各种输出的成功率
- **攻击链识别率**: 成功识别的攻击链数量

### 日志级别

- **DEBUG**: 详细的处理过程日志
- **INFO**: 关键操作和统计信息
- **WARN**: 非致命错误和警告
- **ERROR**: 严重错误和异常

### 故障排查

1. **告警丢失**: 检查 Kafka 连接和消费者组状态
2. **处理延迟**: 检查并行度配置和资源使用情况
3. **输出失败**: 检查目标系统连接和权限配置
4. **内存溢出**: 调整缓存大小和 JVM 参数

## 开发指南

### 添加新的检测器

1. 实现 `CertificateDetector` 接口
2. 在配置中注册新检测器
3. 添加相应的测试用例

### 扩展输出类型

1. 实现新的 Sink 类
2. 在 `AlarmOutputManager` 中添加配置
3. 更新配置文件和文档

### 性能优化

1. 调整批量大小和缓存配置
2. 优化序列化和反序列化
3. 使用异步 I/O 和连接池

## 测试

### 单元测试

```bash
mvn test
```

### 集成测试

```bash
mvn verify -P integration-test
```

### 性能测试

```bash
# 使用 JMeter 或自定义测试工具
./scripts/performance-test.sh
```

## 版本历史

- **v3.0.0**: 初始版本，支持基础告警处理功能
- **v3.0.1**: 添加攻击链分析功能
- **v3.0.2**: 优化性能和稳定性

## 贡献指南

1. Fork 项目
2. 创建功能分支
3. 提交代码变更
4. 创建 Pull Request

## 许可证

本项目采用 MIT 许可证，详见 LICENSE 文件。
