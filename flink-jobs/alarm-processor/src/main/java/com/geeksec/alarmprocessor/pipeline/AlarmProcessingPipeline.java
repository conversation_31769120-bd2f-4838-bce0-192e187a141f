package com.geeksec.alarmprocessor.pipeline;

import com.geeksec.alarmprocessor.config.AlarmProcessorConfig;
import com.geeksec.alarmprocessor.model.Alarm;
import com.geeksec.alarmprocessor.model.AlarmEvent;
import com.geeksec.alarmprocessor.pipeline.function.AlarmEventToAlarmConverter;
import com.geeksec.alarmprocessor.pipeline.function.AttackChainAnalysisFunction;
import com.geeksec.alarmprocessor.pipeline.function.DeduplicationFunction;
import com.geeksec.alarmprocessor.pipeline.function.FormattingFunction;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.flink.streaming.api.datastream.DataStream;

/**
 * 告警处理流水线
 * 实现完整的告警处理流程：转换 -> 去重 -> 格式化 -> 攻击链分析
 * 
 * <AUTHOR>
 * @since 3.0.0
 */
@Slf4j
public class AlarmProcessingPipeline {
    
    /**
     * 构建告警处理流水线
     * 
     * @param alarmEventStream 告警事件数据流
     * @param config 配置对象
     * @return 流水线结果
     */
    public static PipelineResult build(DataStream<AlarmEvent> alarmEventStream, 
                                     AlarmProcessorConfig config) {
        
        log.info("开始构建告警处理流水线");
        
        // 1. 事件转换：将告警事件转换为告警对象
        DataStream<Alarm> alarmStream = alarmEventStream
                .map(new AlarmEventToAlarmConverter())
                .name("告警事件转换")
                .uid("alarm-event-to-alarm-converter");
        
        log.info("告警事件转换组件已添加");
        
        // 2. 告警去重：根据配置进行去重处理
        DataStream<Alarm> dedupedAlarmStream;
        if (config.isDeduplicationEnabled()) {
            dedupedAlarmStream = alarmStream
                    .keyBy(Alarm::getDeduplicationKey)
                    .flatMap(new DeduplicationFunction(config))
                    .name("告警去重")
                    .uid("alarm-deduplication");
            log.info("告警去重组件已添加");
        } else {
            dedupedAlarmStream = alarmStream;
            log.info("告警去重已禁用，跳过去重处理");
        }
        
        // 3. 告警格式化：生成原因分析和处理建议
        DataStream<Alarm> formattedAlarmStream;
        if (config.isFormattingEnabled()) {
            formattedAlarmStream = dedupedAlarmStream
                    .map(new FormattingFunction(config))
                    .name("告警格式化")
                    .uid("alarm-formatting");
            log.info("告警格式化组件已添加");
        } else {
            formattedAlarmStream = dedupedAlarmStream;
            log.info("告警格式化已禁用，跳过格式化处理");
        }
        
        // 4. 攻击链分析：分析告警间的关联关系
        DataStream<Alarm> enrichedAlarmStream;
        if (config.isAttackChainEnabled()) {
            enrichedAlarmStream = formattedAlarmStream
                    .keyBy(Alarm::getAttackChainKey)
                    .process(new AttackChainAnalysisFunction(config))
                    .name("攻击链分析")
                    .uid("attack-chain-analysis");
            log.info("攻击链分析组件已添加");
        } else {
            enrichedAlarmStream = formattedAlarmStream;
            log.info("攻击链分析已禁用，跳过攻击链分析");
        }
        
        // 5. 分流处理：根据告警类型和优先级进行分流
        StreamSplitter splitter = new StreamSplitter(enrichedAlarmStream, config);
        
        DataStream<Alarm> highPriorityAlarms = splitter.getHighPriorityAlarms();
        DataStream<Alarm> normalAlarms = splitter.getNormalAlarms();
        DataStream<Alarm> certificateAlarms = splitter.getCertificateAlarms();
        
        log.info("告警分流组件已添加");
        
        log.info("告警处理流水线构建完成");
        
        return PipelineResult.builder()
                .processedAlarms(enrichedAlarmStream)
                .highPriorityAlarms(highPriorityAlarms)
                .normalAlarms(normalAlarms)
                .certificateAlarms(certificateAlarms)
                .build();
    }
    
    /**
     * 流水线结果
     */
    @Data
    @lombok.Builder
    public static class PipelineResult {
        
        /** 处理后的所有告警流 */
        private DataStream<Alarm> processedAlarms;
        
        /** 高优先级告警流 */
        private DataStream<Alarm> highPriorityAlarms;
        
        /** 普通告警流 */
        private DataStream<Alarm> normalAlarms;
        
        /** 证书相关告警流 */
        private DataStream<Alarm> certificateAlarms;
    }
    
    /**
     * 流分割器
     * 根据告警属性将流分割为不同的子流
     */
    private static class StreamSplitter {
        
        private final DataStream<Alarm> highPriorityAlarms;
        private final DataStream<Alarm> normalAlarms;
        private final DataStream<Alarm> certificateAlarms;
        
        public StreamSplitter(DataStream<Alarm> alarmStream, AlarmProcessorConfig config) {
            // 高优先级告警过滤
            this.highPriorityAlarms = alarmStream
                    .filter(Alarm::isHighPriority)
                    .name("高优先级告警过滤")
                    .uid("high-priority-alarm-filter");
            
            // 普通告警过滤
            this.normalAlarms = alarmStream
                    .filter(alarm -> !alarm.isHighPriority())
                    .name("普通告警过滤")
                    .uid("normal-alarm-filter");
            
            // 证书相关告警过滤
            this.certificateAlarms = alarmStream
                    .filter(Alarm::isCertificateAlarm)
                    .name("证书告警过滤")
                    .uid("certificate-alarm-filter");
        }
        
        public DataStream<Alarm> getHighPriorityAlarms() {
            return highPriorityAlarms;
        }
        
        public DataStream<Alarm> getNormalAlarms() {
            return normalAlarms;
        }
        
        public DataStream<Alarm> getCertificateAlarms() {
            return certificateAlarms;
        }
    }
}
