package com.geeksec.alarmprocessor.output;

import com.geeksec.alarmprocessor.config.AlarmProcessorConfig;
import com.geeksec.alarmprocessor.model.Alarm;
import com.geeksec.alarmprocessor.output.sink.DorisAlarmSink;
import com.geeksec.alarmprocessor.output.sink.KafkaNotificationSink;
import com.geeksec.alarmprocessor.pipeline.AlarmProcessingPipeline;
import lombok.extern.slf4j.Slf4j;
import org.apache.flink.streaming.api.datastream.DataStream;

/**
 * 告警输出管理器
 * 负责配置和管理所有告警输出
 * 
 * <AUTHOR>
 * @since 3.0.0
 */
@Slf4j
public class AlarmOutputManager {
    
    /**
     * 私有构造方法，防止实例化
     */
    private AlarmOutputManager() {
        // 工具类，禁止实例化
    }
    
    /**
     * 配置所有告警输出
     * 
     * @param pipelineResult 流水线结果
     * @param config 配置对象
     */
    public static void configureAllOutputs(AlarmProcessingPipeline.PipelineResult pipelineResult,
                                         AlarmProcessorConfig config) {
        
        log.info("开始配置告警输出");
        
        try {
            // 1. 配置主要告警输出（Doris）
            configureMainAlarmOutput(pipelineResult, config);
            
            // 2. 配置高优先级告警特殊处理
            configureHighPriorityAlarmOutput(pipelineResult, config);
            
            // 3. 配置证书告警特殊处理
            configureCertificateAlarmOutput(pipelineResult, config);
            
            // 4. 配置通知输出（Kafka）
            configureNotificationOutput(pipelineResult, config);
            
            // 5. 配置可选的 Elasticsearch 输出
            configureElasticsearchOutput(pipelineResult, config);
            
            log.info("告警输出配置完成");
            
        } catch (Exception e) {
            log.error("配置告警输出时发生异常: {}", e.getMessage(), e);
            throw new RuntimeException("告警输出配置失败", e);
        }
    }
    
    /**
     * 配置主要告警输出（Doris）
     */
    private static void configureMainAlarmOutput(AlarmProcessingPipeline.PipelineResult pipelineResult,
                                               AlarmProcessorConfig config) {
        
        if (!config.isDorisEnabled()) {
            log.info("Doris 输出已禁用，跳过配置");
            return;
        }
        
        log.info("配置主要告警输出到 Doris");
        
        DataStream<Alarm> processedAlarms = pipelineResult.getProcessedAlarms();
        
        // 添加 Doris Sink
        processedAlarms
                .addSink(new DorisAlarmSink(config))
                .name("告警Doris输出")
                .uid("alarm-doris-sink")
                .setParallelism(Math.max(1, config.getJobParallelism() / 2));
        
        log.info("主要告警输出配置完成");
    }
    
    /**
     * 配置高优先级告警特殊处理
     */
    private static void configureHighPriorityAlarmOutput(AlarmProcessingPipeline.PipelineResult pipelineResult,
                                                        AlarmProcessorConfig config) {
        
        log.info("配置高优先级告警特殊处理");
        
        DataStream<Alarm> highPriorityAlarms = pipelineResult.getHighPriorityAlarms();
        
        // 高优先级告警的特殊处理
        if (config.isDorisEnabled()) {
            // 使用更小的批量大小，更快的处理速度
            AlarmProcessorConfig highPriorityConfig = createHighPriorityConfig(config);
            
            highPriorityAlarms
                    .addSink(new DorisAlarmSink(highPriorityConfig))
                    .name("高优先级告警Doris输出")
                    .uid("high-priority-alarm-doris-sink")
                    .setParallelism(2);
        }
        
        // 高优先级告警的即时通知
        if (config.isNotificationEnabled()) {
            highPriorityAlarms
                    .addSink(new KafkaNotificationSink(config, "high-priority"))
                    .name("高优先级告警通知")
                    .uid("high-priority-alarm-notification")
                    .setParallelism(1);
        }
        
        log.info("高优先级告警特殊处理配置完成");
    }
    
    /**
     * 配置证书告警特殊处理
     */
    private static void configureCertificateAlarmOutput(AlarmProcessingPipeline.PipelineResult pipelineResult,
                                                       AlarmProcessorConfig config) {
        
        log.info("配置证书告警特殊处理");
        
        DataStream<Alarm> certificateAlarms = pipelineResult.getCertificateAlarms();
        
        // 证书告警的特殊通知
        if (config.isNotificationEnabled()) {
            certificateAlarms
                    .addSink(new KafkaNotificationSink(config, "certificate"))
                    .name("证书告警通知")
                    .uid("certificate-alarm-notification")
                    .setParallelism(1);
        }
        
        // 可以添加其他证书告警的特殊处理逻辑
        // 例如：发送到证书管理系统、更新证书黑名单等
        
        log.info("证书告警特殊处理配置完成");
    }
    
    /**
     * 配置通知输出（Kafka）
     */
    private static void configureNotificationOutput(AlarmProcessingPipeline.PipelineResult pipelineResult,
                                                   AlarmProcessorConfig config) {
        
        if (!config.isNotificationEnabled()) {
            log.info("通知输出已禁用，跳过配置");
            return;
        }
        
        log.info("配置通知输出到 Kafka");
        
        DataStream<Alarm> processedAlarms = pipelineResult.getProcessedAlarms();
        
        // 添加通知 Sink
        processedAlarms
                .addSink(new KafkaNotificationSink(config, "general"))
                .name("告警通知输出")
                .uid("alarm-notification-sink")
                .setParallelism(Math.max(1, config.getJobParallelism() / 4));
        
        log.info("通知输出配置完成");
    }
    
    /**
     * 配置 Elasticsearch 输出（可选）
     */
    private static void configureElasticsearchOutput(AlarmProcessingPipeline.PipelineResult pipelineResult,
                                                    AlarmProcessorConfig config) {
        
        if (!config.isElasticsearchEnabled()) {
            log.info("Elasticsearch 输出已禁用，跳过配置");
            return;
        }
        
        log.info("配置 Elasticsearch 输出");
        
        DataStream<Alarm> processedAlarms = pipelineResult.getProcessedAlarms();
        
        // TODO: 实现 Elasticsearch Sink
        // processedAlarms
        //         .addSink(new ElasticsearchAlarmSink(config))
        //         .name("告警Elasticsearch输出")
        //         .uid("alarm-elasticsearch-sink")
        //         .setParallelism(Math.max(1, config.getJobParallelism() / 4));
        
        log.info("Elasticsearch 输出配置完成（暂未实现）");
    }
    
    /**
     * 创建高优先级告警的配置
     */
    private static AlarmProcessorConfig createHighPriorityConfig(AlarmProcessorConfig baseConfig) {
        AlarmProcessorConfig highPriorityConfig = new AlarmProcessorConfig();
        
        // 复制基础配置
        copyBaseConfig(baseConfig, highPriorityConfig);
        
        // 调整高优先级告警的特殊配置
        highPriorityConfig.setDorisBatchSize(baseConfig.getDorisBatchSize() / 2); // 更小的批量
        highPriorityConfig.setDorisBatchInterval(baseConfig.getDorisBatchInterval() / 2); // 更短的间隔
        highPriorityConfig.setDorisLabelPrefix(baseConfig.getDorisLabelPrefix() + "-high-priority");
        
        return highPriorityConfig;
    }
    
    /**
     * 复制基础配置
     */
    private static void copyBaseConfig(AlarmProcessorConfig source, AlarmProcessorConfig target) {
        target.setDorisEnabled(source.isDorisEnabled());
        target.setDorisFeNodes(source.getDorisFeNodes());
        target.setDorisDatabase(source.getDorisDatabase());
        target.setDorisTable(source.getDorisTable());
        target.setDorisUsername(source.getDorisUsername());
        target.setDorisPassword(source.getDorisPassword());
        target.setDorisBatchSize(source.getDorisBatchSize());
        target.setDorisBatchInterval(source.getDorisBatchInterval());
        target.setDorisLabelPrefix(source.getDorisLabelPrefix());
        
        target.setNotificationEnabled(source.isNotificationEnabled());
        target.setKafkaBootstrapServers(source.getKafkaBootstrapServers());
        target.setNotificationTopic(source.getNotificationTopic());
        target.setNotificationBatchSize(source.getNotificationBatchSize());
        target.setNotificationLingerMs(source.getNotificationLingerMs());
    }
    
    /**
     * 添加调试输出（开发和测试时使用）
     */
    public static void addDebugOutputs(AlarmProcessingPipeline.PipelineResult pipelineResult,
                                     AlarmProcessorConfig config) {
        
        if (!config.isMonitoringEnabled()) {
            return;
        }
        
        log.info("添加调试输出");
        
        // 添加日志输出用于调试
        pipelineResult.getProcessedAlarms()
                .addSink(new DebugAlarmSink())
                .name("调试日志输出")
                .uid("debug-alarm-sink")
                .setParallelism(1);
        
        log.info("调试输出添加完成");
    }
    
    /**
     * 调试告警 Sink
     */
    private static class DebugAlarmSink implements org.apache.flink.streaming.api.functions.sink.SinkFunction<Alarm> {
        
        private static final long serialVersionUID = 1L;
        
        @Override
        public void invoke(Alarm alarm, Context context) {
            log.info("调试输出告警: ID={}, 类型={}, 级别={}, 源IP={}, 目标IP={}, 处理时间={}",
                    alarm.getAlarmId(),
                    alarm.getAlarmType(),
                    alarm.getAlarmLevel(),
                    alarm.getSrcIp(),
                    alarm.getDstIp(),
                    alarm.getProcessedTimestamp());
        }
    }
}
