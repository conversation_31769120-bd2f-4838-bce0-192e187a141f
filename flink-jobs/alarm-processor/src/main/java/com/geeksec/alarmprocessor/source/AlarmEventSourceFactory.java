package com.geeksec.alarmprocessor.source;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;
import com.geeksec.alarmprocessor.config.AlarmProcessorConfig;
import com.geeksec.alarmprocessor.model.AlarmEvent;
import lombok.extern.slf4j.Slf4j;
import org.apache.flink.api.common.eventtime.WatermarkStrategy;
import org.apache.flink.api.common.serialization.SimpleStringSchema;
import org.apache.flink.connector.kafka.source.KafkaSource;
import org.apache.flink.connector.kafka.source.enumerator.initializer.OffsetsInitializer;
import org.apache.flink.streaming.api.datastream.DataStream;
import org.apache.flink.streaming.api.environment.StreamExecutionEnvironment;

import java.time.Duration;

/**
 * 告警事件数据源工厂
 * 负责创建和配置告警事件的 Kafka 数据源
 * 
 * <AUTHOR>
 * @since 3.0.0
 */
@Slf4j
public class AlarmEventSourceFactory {
    
    private static final ObjectMapper OBJECT_MAPPER = new ObjectMapper()
            .registerModule(new JavaTimeModule());
    
    /**
     * 创建告警事件数据源
     * 
     * @param env 流执行环境
     * @param config 配置对象
     * @return 告警事件数据流
     */
    public static DataStream<AlarmEvent> createAlarmEventSource(
            StreamExecutionEnvironment env, 
            AlarmProcessorConfig config) {
        
        log.info("创建告警事件数据源，主题: {}", config.getInputTopic());
        
        // 创建 Kafka 源
        KafkaSource<String> kafkaSource = createKafkaSource(config);
        
        // 创建原始字符串数据流
        DataStream<String> rawStream = env.fromSource(
                kafkaSource,
                createWatermarkStrategy(),
                "告警事件Kafka源"
        );
        
        // 转换为告警事件对象
        DataStream<AlarmEvent> alarmEventStream = rawStream
                .flatMap(new AlarmEventDeserializer(OBJECT_MAPPER))
                .name("告警事件反序列化")
                .uid("alarm-event-deserializer");
        
        // 添加数据验证
        DataStream<AlarmEvent> validatedStream = alarmEventStream
                .filter(new AlarmEventValidator())
                .name("告警事件验证")
                .uid("alarm-event-validator");
        
        log.info("告警事件数据源创建完成");
        return validatedStream;
    }
    
    /**
     * 创建 Kafka 源配置
     */
    private static KafkaSource<String> createKafkaSource(AlarmProcessorConfig config) {
        // 确定起始偏移量
        OffsetsInitializer offsetsInitializer;
        switch (config.getStartingOffsets().toLowerCase()) {
            case "earliest":
                offsetsInitializer = OffsetsInitializer.earliest();
                break;
            case "latest":
                offsetsInitializer = OffsetsInitializer.latest();
                break;
            default:
                offsetsInitializer = OffsetsInitializer.latest();
                log.warn("未知的起始偏移量配置: {}，使用默认值 latest", config.getStartingOffsets());
        }
        
        return KafkaSource.<String>builder()
                .setBootstrapServers(config.getKafkaBootstrapServers())
                .setTopics(config.getInputTopic())
                .setGroupId(config.getKafkaGroupId())
                .setStartingOffsets(offsetsInitializer)
                .setValueOnlyDeserializer(new SimpleStringSchema())
                .setProperty("enable.auto.commit", String.valueOf(config.isAutoCommit()))
                .setProperty("auto.commit.interval.ms", String.valueOf(config.getCommitInterval()))
                .setProperty("session.timeout.ms", "30000")
                .setProperty("heartbeat.interval.ms", "10000")
                .setProperty("max.poll.records", "500")
                .setProperty("fetch.min.bytes", "1")
                .setProperty("fetch.max.wait.ms", "500")
                .build();
    }
    
    /**
     * 创建水印策略
     */
    private static WatermarkStrategy<String> createWatermarkStrategy() {
        return WatermarkStrategy
                .<String>forBoundedOutOfOrderness(Duration.ofSeconds(10))
                .withIdleness(Duration.ofMinutes(1));
    }
}
