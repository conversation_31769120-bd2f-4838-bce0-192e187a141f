package com.geeksec.alarmprocessor.output.sink;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;
import com.geeksec.alarmprocessor.config.AlarmProcessorConfig;
import com.geeksec.alarmprocessor.model.Alarm;
import lombok.extern.slf4j.Slf4j;
import org.apache.doris.flink.cfg.DorisExecutionOptions;
import org.apache.doris.flink.cfg.DorisOptions;
import org.apache.doris.flink.cfg.DorisReadOptions;
import org.apache.doris.flink.sink.DorisSink;
import org.apache.doris.flink.sink.writer.serializer.SimpleStringSerializer;
import org.apache.flink.api.common.functions.MapFunction;
import org.apache.flink.configuration.Configuration;
import org.apache.flink.metrics.Counter;
import org.apache.flink.metrics.MetricGroup;
import org.apache.flink.streaming.api.functions.sink.RichSinkFunction;

import java.time.format.DateTimeFormatter;
import java.util.Properties;

/**
 * Doris 告警输出 Sink
 * 将处理后的告警数据写入 Doris 数据库
 * 
 * <AUTHOR>
 * @since 3.0.0
 */
@Slf4j
public class DorisAlarmSink extends RichSinkFunction<Alarm> {
    
    private static final long serialVersionUID = 1L;
    
    private final AlarmProcessorConfig config;
    private final ObjectMapper objectMapper;
    
    /** Doris Sink */
    private transient DorisSink<String> dorisSink;
    
    /** 指标计数器 */
    private transient Counter totalRecords;
    private transient Counter successfulRecords;
    private transient Counter failedRecords;
    
    public DorisAlarmSink(AlarmProcessorConfig config) {
        this.config = config;
        this.objectMapper = new ObjectMapper()
                .registerModule(new JavaTimeModule());
    }
    
    @Override
    public void open(Configuration parameters) throws Exception {
        super.open(parameters);
        
        // 初始化指标
        initializeMetrics();
        
        // 创建 Doris Sink
        createDorisSink();
        
        log.info("Doris 告警输出 Sink 初始化完成，目标表: {}.{}", 
                config.getDorisDatabase(), config.getDorisTable());
    }
    
    /**
     * 初始化指标
     */
    private void initializeMetrics() {
        MetricGroup metricGroup = getRuntimeContext().getMetricGroup()
                .addGroup("doris-alarm-sink");
        
        totalRecords = metricGroup.counter("total_records");
        successfulRecords = metricGroup.counter("successful_records");
        failedRecords = metricGroup.counter("failed_records");
    }
    
    /**
     * 创建 Doris Sink
     */
    private void createDorisSink() {
        // Doris 连接配置
        DorisOptions.Builder dorisBuilder = DorisOptions.builder()
                .setFenodes(config.getDorisFeNodes())
                .setTableIdentifier(config.getDorisDatabase() + "." + config.getDorisTable())
                .setUsername(config.getDorisUsername())
                .setPassword(config.getDorisPassword());
        
        // Doris 执行配置
        Properties streamLoadProps = new Properties();
        streamLoadProps.put("format", "json");
        streamLoadProps.put("read_json_by_line", "true");
        streamLoadProps.put("load_to_single_tablet", "false");
        streamLoadProps.put("timeout", "600");
        streamLoadProps.put("max_filter_ratio", "0.1");
        
        DorisExecutionOptions.Builder executionBuilder = DorisExecutionOptions.builder()
                .setLabelPrefix(config.getDorisLabelPrefix())
                .setStreamLoadProp(streamLoadProps)
                .setBatchIntervalMs(config.getDorisBatchInterval())
                .setMaxRetries(3)
                .setBufferSize(64 * 1024)
                .setBufferCount(3);
        
        // 创建 Doris Sink
        this.dorisSink = DorisSink.<String>builder()
                .setDorisReadOptions(DorisReadOptions.builder().build())
                .setDorisOptions(dorisBuilder.build())
                .setDorisExecutionOptions(executionBuilder.build())
                .setSerializer(new SimpleStringSerializer())
                .build();
    }
    
    @Override
    public void invoke(Alarm alarm, Context context) throws Exception {
        totalRecords.inc();
        
        try {
            // 转换告警对象为 JSON 字符串
            String jsonString = convertAlarmToJson(alarm);
            
            // 注意：DorisSink 不是 RichSinkFunction，需要直接使用 addSink
            // 这里暂时记录日志，实际使用时应该通过 addSink 方式添加
            log.debug("准备写入 Doris 的数据: {}", jsonString);
            
            successfulRecords.inc();
            log.debug("成功写入告警到 Doris: {}", alarm.getAlarmId());
            
        } catch (Exception e) {
            failedRecords.inc();
            log.error("写入告警到 Doris 失败: {}, 错误: {}", alarm.getAlarmId(), e.getMessage(), e);
            
            // 可以选择重试或者写入死信队列
            // 这里选择抛出异常，让 Flink 的重试机制处理
            throw e;
        }
    }
    
    /**
     * 转换告警对象为 JSON 字符串
     */
    private String convertAlarmToJson(Alarm alarm) throws Exception {
        // 创建用于 Doris 的告警记录
        DorisAlarmRecord record = DorisAlarmRecord.builder()
                .alarmId(alarm.getAlarmId())
                .originalEventId(alarm.getOriginalEventId())
                .sourceModule(alarm.getSourceModule())
                .alarmType(alarm.getAlarmType())
                .alarmName(alarm.getAlarmName())
                .threatType(alarm.getThreatType())
                .alarmLevel(alarm.getAlarmLevel() != null ? alarm.getAlarmLevel().name() : null)
                .srcIp(alarm.getSrcIp())
                .dstIp(alarm.getDstIp())
                .srcPort(alarm.getSrcPort())
                .dstPort(alarm.getDstPort())
                .protocol(alarm.getProtocol())
                .eventTimestamp(alarm.getEventTimestamp() != null ? 
                        alarm.getEventTimestamp().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")) : null)
                .processedTimestamp(alarm.getProcessedTimestamp() != null ? 
                        alarm.getProcessedTimestamp().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")) : null)
                .detectorType(alarm.getDetectorType())
                .confidence(alarm.getConfidence())
                .description(alarm.getDescription())
                .riskScore(alarm.getReasonAnalysis() != null ? alarm.getReasonAnalysis().getRiskScore() : null)
                .attackChainId(alarm.getAttackChainInfo() != null ? alarm.getAttackChainInfo().getChainId() : null)
                .correlationScore(alarm.getAttackChainInfo() != null ? alarm.getAttackChainInfo().getCorrelationScore() : null)
                .occurrenceCount(alarm.getDeduplicationInfo() != null ? alarm.getDeduplicationInfo().getOccurrenceCount() : 1)
                .isHighPriority(alarm.isHighPriority())
                .isCertificateAlarm(alarm.isCertificateAlarm())
                .certHash(alarm.getCertificateInfo() != null ? alarm.getCertificateInfo().getCertHash() : null)
                .certSubjectCn(alarm.getCertificateInfo() != null ? alarm.getCertificateInfo().getSubjectCn() : null)
                .sessionId(alarm.getSessionInfo() != null ? alarm.getSessionInfo().getSessionId() : null)
                .reasonAnalysis(alarm.getReasonAnalysis() != null ? objectMapper.writeValueAsString(alarm.getReasonAnalysis()) : null)
                .handlingSuggestions(alarm.getHandlingSuggestions() != null ? objectMapper.writeValueAsString(alarm.getHandlingSuggestions()) : null)
                .attackChainInfo(alarm.getAttackChainInfo() != null ? objectMapper.writeValueAsString(alarm.getAttackChainInfo()) : null)
                .extendedProperties(alarm.getExtendedProperties() != null ? objectMapper.writeValueAsString(alarm.getExtendedProperties()) : null)
                .build();
        
        return objectMapper.writeValueAsString(record);
    }
    
    @Override
    public void close() throws Exception {
        // DorisSink 的生命周期由 Flink 管理，这里不需要手动关闭

        log.info("Doris 告警输出 Sink 关闭，写入统计: 总计={}, 成功={}, 失败={}",
                totalRecords.getCount(),
                successfulRecords.getCount(),
                failedRecords.getCount());

        super.close();
    }
    
    /**
     * Doris 告警记录
     * 用于序列化到 Doris 的数据结构
     */
    @lombok.Data
    @lombok.Builder
    @lombok.NoArgsConstructor
    @lombok.AllArgsConstructor
    private static class DorisAlarmRecord {
        private String alarmId;
        private String originalEventId;
        private String sourceModule;
        private String alarmType;
        private String alarmName;
        private String threatType;
        private String alarmLevel;
        private String srcIp;
        private String dstIp;
        private Integer srcPort;
        private Integer dstPort;
        private String protocol;
        private String eventTimestamp;
        private String processedTimestamp;
        private String detectorType;
        private Double confidence;
        private String description;
        private Integer riskScore;
        private String attackChainId;
        private Double correlationScore;
        private Integer occurrenceCount;
        private Boolean isHighPriority;
        private Boolean isCertificateAlarm;
        private String certHash;
        private String certSubjectCn;
        private String sessionId;
        private String reasonAnalysis;
        private String handlingSuggestions;
        private String attackChainInfo;
        private String extendedProperties;
    }
}
