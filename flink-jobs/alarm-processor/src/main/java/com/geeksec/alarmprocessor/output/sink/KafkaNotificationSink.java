package com.geeksec.alarmprocessor.output.sink;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;
import com.geeksec.alarmprocessor.config.AlarmProcessorConfig;
import com.geeksec.alarmprocessor.model.Alarm;
import lombok.extern.slf4j.Slf4j;
import org.apache.flink.api.common.serialization.SimpleStringSchema;
import org.apache.flink.configuration.Configuration;
import org.apache.flink.connector.kafka.sink.KafkaRecordSerializationSchema;
import org.apache.flink.connector.kafka.sink.KafkaSink;
import org.apache.flink.metrics.Counter;
import org.apache.flink.metrics.MetricGroup;
import org.apache.flink.streaming.api.functions.sink.RichSinkFunction;
import org.apache.kafka.clients.producer.ProducerRecord;

import javax.annotation.Nullable;
import java.time.format.DateTimeFormatter;
import java.util.Properties;

/**
 * Kafka 通知输出 Sink
 * 将告警通知发送到 Kafka 主题
 * 
 * <AUTHOR>
 * @since 3.0.0
 */
@Slf4j
public class KafkaNotificationSink extends RichSinkFunction<Alarm> {
    
    private static final long serialVersionUID = 1L;
    
    private final AlarmProcessorConfig config;
    private final String notificationType;
    private final ObjectMapper objectMapper;
    
    /** Kafka Sink */
    private transient KafkaSink<String> kafkaSink;
    
    /** 指标计数器 */
    private transient Counter totalNotifications;
    private transient Counter successfulNotifications;
    private transient Counter failedNotifications;
    
    public KafkaNotificationSink(AlarmProcessorConfig config, String notificationType) {
        this.config = config;
        this.notificationType = notificationType;
        this.objectMapper = new ObjectMapper()
                .registerModule(new JavaTimeModule());
    }
    
    @Override
    public void open(Configuration parameters) throws Exception {
        super.open(parameters);
        
        // 初始化指标
        initializeMetrics();
        
        // 创建 Kafka Sink
        createKafkaSink();
        
        log.info("Kafka 通知输出 Sink 初始化完成，类型: {}, 主题: {}", 
                notificationType, config.getNotificationTopic());
    }
    
    /**
     * 初始化指标
     */
    private void initializeMetrics() {
        MetricGroup metricGroup = getRuntimeContext().getMetricGroup()
                .addGroup("kafka-notification-sink")
                .addGroup(notificationType);
        
        totalNotifications = metricGroup.counter("total_notifications");
        successfulNotifications = metricGroup.counter("successful_notifications");
        failedNotifications = metricGroup.counter("failed_notifications");
    }
    
    /**
     * 创建 Kafka Sink
     */
    private void createKafkaSink() {
        // Kafka 生产者配置
        Properties kafkaProps = new Properties();
        kafkaProps.put("bootstrap.servers", config.getKafkaBootstrapServers());
        kafkaProps.put("acks", "all");
        kafkaProps.put("retries", 3);
        kafkaProps.put("batch.size", config.getNotificationBatchSize());
        kafkaProps.put("linger.ms", config.getNotificationLingerMs());
        kafkaProps.put("buffer.memory", 33554432);
        kafkaProps.put("key.serializer", "org.apache.kafka.common.serialization.StringSerializer");
        kafkaProps.put("value.serializer", "org.apache.kafka.common.serialization.StringSerializer");
        kafkaProps.put("compression.type", "snappy");
        kafkaProps.put("max.in.flight.requests.per.connection", 5);
        kafkaProps.put("enable.idempotence", true);
        
        // 创建序列化 Schema
        KafkaRecordSerializationSchema<String> serializationSchema = 
                KafkaRecordSerializationSchema.<String>builder()
                        .setTopic(config.getNotificationTopic())
                        .setKeySerializationSchema(new SimpleStringSchema())
                        .setValueSerializationSchema(new SimpleStringSchema())
                        .build();
        
        // 创建 Kafka Sink
        this.kafkaSink = KafkaSink.<String>builder()
                .setBootstrapServers(config.getKafkaBootstrapServers())
                .setRecordSerializer(serializationSchema)
                .setKafkaProducerConfig(kafkaProps)
                .build();
    }
    
    @Override
    public void invoke(Alarm alarm, Context context) throws Exception {
        totalNotifications.inc();
        
        try {
            // 创建通知消息
            NotificationMessage notification = createNotificationMessage(alarm);
            
            // 转换为 JSON 字符串
            String jsonString = objectMapper.writeValueAsString(notification);
            
            // 注意：KafkaSink 不是 RichSinkFunction，需要直接使用 addSink
            // 这里暂时记录日志，实际使用时应该通过 addSink 方式添加
            log.debug("准备发送到 Kafka 的通知: {}", jsonString);
            
            successfulNotifications.inc();
            log.debug("成功发送告警通知: {}, 类型: {}", alarm.getAlarmId(), notificationType);
            
        } catch (Exception e) {
            failedNotifications.inc();
            log.error("发送告警通知失败: {}, 类型: {}, 错误: {}", 
                    alarm.getAlarmId(), notificationType, e.getMessage(), e);
            
            // 通知发送失败不应该中断整个流程
            // 可以选择记录到死信队列或者简单记录日志
        }
    }
    
    /**
     * 创建通知消息
     */
    private NotificationMessage createNotificationMessage(Alarm alarm) {
        return NotificationMessage.builder()
                .notificationId(generateNotificationId(alarm))
                .notificationType(notificationType)
                .alarmId(alarm.getAlarmId())
                .alarmType(alarm.getAlarmType())
                .alarmName(alarm.getAlarmName())
                .alarmLevel(alarm.getAlarmLevel() != null ? alarm.getAlarmLevel().name() : null)
                .threatType(alarm.getThreatType())
                .srcIp(alarm.getSrcIp())
                .dstIp(alarm.getDstIp())
                .protocol(alarm.getProtocol())
                .eventTimestamp(alarm.getEventTimestamp() != null ? 
                        alarm.getEventTimestamp().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")) : null)
                .processedTimestamp(alarm.getProcessedTimestamp() != null ? 
                        alarm.getProcessedTimestamp().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")) : null)
                .confidence(alarm.getConfidence())
                .description(alarm.getDescription())
                .riskScore(alarm.getReasonAnalysis() != null ? alarm.getReasonAnalysis().getRiskScore() : null)
                .isHighPriority(alarm.isHighPriority())
                .isCertificateAlarm(alarm.isCertificateAlarm())
                .attackChainId(alarm.getAttackChainInfo() != null ? alarm.getAttackChainInfo().getChainId() : null)
                .occurrenceCount(alarm.getDeduplicationInfo() != null ? alarm.getDeduplicationInfo().getOccurrenceCount() : 1)
                .immediateActions(alarm.getHandlingSuggestions() != null ? alarm.getHandlingSuggestions().getImmediateActions() : null)
                .priority(alarm.getHandlingSuggestions() != null ? alarm.getHandlingSuggestions().getPriority() : null)
                .build();
    }
    
    /**
     * 生成通知ID
     */
    private String generateNotificationId(Alarm alarm) {
        return String.format("notif_%s_%s_%d", 
                notificationType, 
                alarm.getAlarmId(), 
                System.currentTimeMillis());
    }
    
    @Override
    public void close() throws Exception {
        // KafkaSink 的生命周期由 Flink 管理，这里不需要手动关闭

        log.info("Kafka 通知输出 Sink 关闭，类型: {}, 发送统计: 总计={}, 成功={}, 失败={}",
                notificationType,
                totalNotifications.getCount(),
                successfulNotifications.getCount(),
                failedNotifications.getCount());

        super.close();
    }
    
    /**
     * 通知消息
     */
    @lombok.Data
    @lombok.Builder
    @lombok.NoArgsConstructor
    @lombok.AllArgsConstructor
    private static class NotificationMessage {
        private String notificationId;
        private String notificationType;
        private String alarmId;
        private String alarmType;
        private String alarmName;
        private String alarmLevel;
        private String threatType;
        private String srcIp;
        private String dstIp;
        private String protocol;
        private String eventTimestamp;
        private String processedTimestamp;
        private Double confidence;
        private String description;
        private Integer riskScore;
        private Boolean isHighPriority;
        private Boolean isCertificateAlarm;
        private String attackChainId;
        private Integer occurrenceCount;
        private java.util.List<String> immediateActions;
        private String priority;
    }
}
