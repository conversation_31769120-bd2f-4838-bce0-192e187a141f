package com.geeksec.alarmprocessor.pipeline.function;

import com.geeksec.alarmprocessor.model.Alarm;
import com.geeksec.alarmprocessor.model.AlarmEvent;
import lombok.extern.slf4j.Slf4j;
import org.apache.flink.api.common.functions.RichMapFunction;
import org.apache.flink.configuration.Configuration;
import org.apache.flink.metrics.Counter;
import org.apache.flink.metrics.MetricGroup;

import java.time.LocalDateTime;
import java.util.UUID;

/**
 * 告警事件到告警对象转换器
 * 将原始的告警事件转换为可处理的告警对象
 * 
 * <AUTHOR>
 * @since 3.0.0
 */
@Slf4j
public class AlarmEventToAlarmConverter extends RichMapFunction<AlarmEvent, Alarm> {
    
    private static final long serialVersionUID = 1L;
    
    // 指标计数器
    private transient Counter totalConversions;
    private transient Counter successfulConversions;
    private transient Counter failedConversions;
    
    @Override
    public void open(Configuration parameters) throws Exception {
        super.open(parameters);
        
        // 初始化指标
        MetricGroup metricGroup = getRuntimeContext().getMetricGroup()
                .addGroup("alarm-event-converter");
        
        totalConversions = metricGroup.counter("total_conversions");
        successfulConversions = metricGroup.counter("successful_conversions");
        failedConversions = metricGroup.counter("failed_conversions");
        
        log.info("告警事件转换器初始化完成");
    }
    
    @Override
    public Alarm map(AlarmEvent alarmEvent) throws Exception {
        totalConversions.inc();
        
        try {
            // 生成唯一的告警ID
            String alarmId = generateAlarmId(alarmEvent);
            
            // 创建告警对象
            Alarm alarm = Alarm.builder()
                    .alarmId(alarmId)
                    .originalEventId(alarmEvent.getEventId())
                    .sourceModule(alarmEvent.getSourceModule())
                    .alarmType(alarmEvent.getAlarmType())
                    .alarmName(alarmEvent.getAlarmName())
                    .threatType(alarmEvent.getThreatType())
                    .alarmLevel(alarmEvent.getAlarmLevel())
                    .srcIp(alarmEvent.getSrcIp())
                    .dstIp(alarmEvent.getDstIp())
                    .srcPort(alarmEvent.getSrcPort())
                    .dstPort(alarmEvent.getDstPort())
                    .protocol(alarmEvent.getProtocol())
                    .eventTimestamp(alarmEvent.getTimestamp())
                    .processedTimestamp(LocalDateTime.now())
                    .detectorType(alarmEvent.getDetectorType())
                    .confidence(alarmEvent.getConfidence())
                    .description(alarmEvent.getDescription())
                    .labels(alarmEvent.getLabels())
                    .extendedProperties(alarmEvent.getExtendedProperties())
                    .certificateInfo(alarmEvent.getCertificateInfo())
                    .sessionInfo(alarmEvent.getSessionInfo())
                    .build();
            
            // 初始化处理状态
            Alarm.ProcessingStatus processingStatus = Alarm.ProcessingStatus.builder()
                    .isDeduplicated(false)
                    .isFormatted(false)
                    .isAttackChainAnalyzed(false)
                    .build();
            alarm.setProcessingStatus(processingStatus);
            
            // 初始化去重信息
            Alarm.DeduplicationInfo deduplicationInfo = Alarm.DeduplicationInfo.builder()
                    .dedupKey(alarm.getDeduplicationKey())
                    .firstOccurrence(alarm.getEventTimestamp())
                    .occurrenceCount(1)
                    .lastOccurrence(alarm.getEventTimestamp())
                    .build();
            alarm.setDeduplicationInfo(deduplicationInfo);
            
            successfulConversions.inc();
            log.debug("成功转换告警事件: {} -> {}", alarmEvent.getEventId(), alarmId);
            
            return alarm;
            
        } catch (Exception e) {
            failedConversions.inc();
            log.error("转换告警事件失败: {}", e.getMessage(), e);
            throw e;
        }
    }
    
    /**
     * 生成告警ID
     * 使用UUID确保唯一性，同时包含时间戳信息便于排序
     */
    private String generateAlarmId(AlarmEvent alarmEvent) {
        // 使用时间戳前缀 + UUID 的方式生成ID
        long timestamp = System.currentTimeMillis();
        String uuid = UUID.randomUUID().toString().replace("-", "");
        
        return String.format("%d_%s", timestamp, uuid.substring(0, 8));
    }
    
    @Override
    public void close() throws Exception {
        log.info("告警事件转换器关闭，转换统计: 总计={}, 成功={}, 失败={}", 
                totalConversions.getCount(), 
                successfulConversions.getCount(), 
                failedConversions.getCount());
        super.close();
    }
}
