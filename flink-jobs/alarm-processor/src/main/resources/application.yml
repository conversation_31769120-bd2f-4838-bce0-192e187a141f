# Alarm Processor 配置文件
alarm:
  processor:
    # 作业基础配置
    job:
      name: "alarm-processor"
      parallelism: 4
      checkpointInterval: 60000  # 1分钟
      restartAttempts: 3
      restartDelay: 10000  # 10秒

    # Kafka 配置
    kafka:
      bootstrapServers: "kafka:9092"
      groupId: "alarm-processor"
      # 输入主题配置
      input:
        topic: "alarm-events"
        startingOffsets: "latest"
        autoCommit: true
        commitInterval: 5000
      # 输出主题配置
      output:
        topic: "processed-alarms"
        batchSize: 100
        lingerMs: 1000

    # 告警处理配置
    processing:
      # 去重配置
      deduplication:
        enabled: true
        mode: "TIME_WINDOW"  # TIME_WINDOW, CONTENT_HASH, HYBRID
        timeWindowMs: 60000  # 1分钟时间窗口
        maxCacheSize: 10000
        cacheExpirationMs: 300000  # 5分钟过期

      # 格式化配置
      formatting:
        enabled: true
        includeReasonAnalysis: true
        includeHandlingSuggestions: true
        cacheEnabled: true
        maxCacheSize: 5000

      # 攻击链分析配置
      attackChain:
        enabled: true
        correlationWindowMs: 300000  # 5分钟关联窗口
        maxCacheSize: 15000
        minEventsForChain: 2

      # 批量处理配置
      batch:
        enabled: true
        maxBatchSize: 50
        maxWaitTimeMs: 30000  # 30秒
        checkIntervalMs: 5000  # 5秒检查间隔

    # 输出配置
    output:
      # Doris 配置
      doris:
        enabled: true
        feNodes: "doris-fe:8030"
        database: "nta"
        table: "ods_alarm_log"
        username: "root"
        password: ""
        batchSize: 200
        batchInterval: 10000
        labelPrefix: "alarm-processor"

      # Kafka 通知配置
      notification:
        enabled: true
        topic: "alarm-notifications"
        batchSize: 50
        lingerMs: 1000

      # Elasticsearch 配置（可选）
      elasticsearch:
        enabled: false
        hosts: "elasticsearch:9200"
        index: "nta-alarms"
        batchSize: 100

    # 监控配置
    monitoring:
      enabled: true
      metricsInterval: 30000  # 30秒
      performanceLogging: true
      detailedMetrics: false

    # 白名单配置
    whitelist:
      enabled: false
      configPath: "alarm_whitelist.json"
      updateInterval: 300  # 5分钟更新间隔

# 日志配置
logging:
  level:
    com.geeksec.alarmprocessor: DEBUG
    org.apache.flink: INFO
    org.apache.kafka: WARN
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n"
