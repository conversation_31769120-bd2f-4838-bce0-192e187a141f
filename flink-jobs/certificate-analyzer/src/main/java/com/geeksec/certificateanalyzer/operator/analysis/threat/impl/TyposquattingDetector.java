package com.geeksec.certificateanalyzer.operator.analysis.detector.impl;

import java.util.List;
import java.util.Map;

import org.apache.commons.text.similarity.LevenshteinDistance;

import com.geeksec.certificateanalyzer.enums.CertificateLabel;
import com.geeksec.certificateanalyzer.model.cert.X509Certificate;
import com.geeksec.certificateanalyzer.operator.analysis.detector.BaseCertificateDetector;
import com.geeksec.common.knowledge.KnowledgeBaseClient;

import lombok.extern.slf4j.Slf4j;

/**
 * 检测Typosquatting攻击的域名证书
 * 通过计算域名与热门域名排行榜中域名的Levenshtein距离来识别可能的域名仿冒
 *
 * Typosquatting是一种网络攻击技术，攻击者注册与知名网站相似的域名，
 * 利用用户输入错误来获取流量或进行网络钓鱼。
 *
 */
@Slf4j
public class TyposquattingDetector extends BaseCertificateDetector {

    private static final double LEVENSHTEIN_THRESHOLD = 0.2; // 20% 相似度阈值

    /**
     * 知识库客户端
     */
    private final KnowledgeBaseClient knowledgeBaseClient;

    public TyposquattingDetector() {
        super("Typosquatting Domain Detector");
        this.knowledgeBaseClient = new KnowledgeBaseClient();
        log.info("域名仿冒检测器初始化完成，使用懒加载模式");
    }
    


    @Override
    protected void doDetect(X509Certificate cert) {
        if (cert == null) {
            return;
        }

        String fullDomain = cert.getCommonName();
        if (fullDomain == null || fullDomain.isEmpty()) {
            return;
        }

        // 检查域名是否为热门域名 - 懒加载查询
        Integer rank = knowledgeBaseClient.getPopularDomainRank(fullDomain.toLowerCase());
        if (rank != null) {
            return; // 如果是已知热门域名，直接返回
        }

        // 检查是否是通配符域名
        if (fullDomain.startsWith("*.")) {
            fullDomain = fullDomain.substring(2); // 移除通配符部分
        }

        // 懒加载检查是否与热门域名相似
        // 注意：这里需要获取一些热门域名来进行比较，但我们可以限制数量
        checkSimilarityWithPopularDomains(cert, fullDomain);
    }

    /**
     * 懒加载检查域名与热门域名的相似度
     *
     * @param cert 证书对象
     * @param domain 要检查的域名
     */
    private void checkSimilarityWithPopularDomains(X509Certificate cert, String domain) {
        try {
            // 获取前1000个热门域名进行比较（限制数量以提高性能）
            List<Map<String, Object>> popularDomains = knowledgeBaseClient.getPopularDomains(0, 1000);

            for (Map<String, Object> domainInfo : popularDomains) {
                String topDomain = (String) domainInfo.get("domain");
                if (topDomain != null && isDomainSuspiciouslySimilar(domain, topDomain)) {
                    cert.getLabels().add(CertificateLabel.FAKE_HOT_DOMAIN);
                    log.debug("检测到伪装热门域名: {} (相似于 {})", domain, topDomain);
                    return;
                }
            }
        } catch (Exception e) {
            log.warn("检查域名相似度失败: {}", domain, e);
        }
    }

    /**
     * 检查域名是否与热门域名可疑地相似
     *
     * @param domain 要检查的域名
     * @param topDomain 热门域名
     * @return 如果域名可疑地相似返回true，否则返回false
     */
    private boolean isDomainSuspiciouslySimilar(String domain, String topDomain) {
        // 简单长度检查：如果长度差异太大，直接返回false
        if (Math.abs(domain.length() - topDomain.length()) > topDomain.length() * LEVENSHTEIN_THRESHOLD) {
            return false;
        }

        // 计算Levenshtein距离
        int distance = LevenshteinDistance.getDefaultInstance().apply(domain, topDomain);

        // 如果编辑距离在阈值内，则认为可疑
        return distance <= topDomain.length() * LEVENSHTEIN_THRESHOLD;
    }
}
