package com.geeksec.certificateanalyzer.operator.analysis.threat.impl;

import java.util.Arrays;
import java.util.HashSet;
import java.util.Map;
import java.util.Set;

import com.geeksec.certificateanalyzer.config.CertificateConstants;
import com.geeksec.certificateanalyzer.enums.CertificateLabel;
import com.geeksec.certificateanalyzer.model.cert.X509Certificate;
import com.geeksec.certificateanalyzer.operator.analysis.threat.BaseCertificateDetector;

import lombok.extern.slf4j.Slf4j;

/**
 * 检测使用OpenSSL生成的自签名证书
 */
@Slf4j
public class OpenSSLSelfSignDetector extends BaseCertificateDetector {

    // OpenSSL默认包含的字段
    private static final Set<String> OPENSSL_FIELDS = new HashSet<>(Arrays.asList(
            CertificateConstants.FIELD_C, CertificateConstants.FIELD_ST, CertificateConstants.FIELD_L,
            CertificateConstants.FIELD_O, CertificateConstants.FIELD_OU, CertificateConstants.FIELD_CN,
            "EMAILADDRESS"));

    public OpenSSLSelfSignDetector() {
        super("OpenSSL Self-Signed Detector");
    }

    /**
     * 检查是否为OpenSSL生成的自签名证书
     * 
     * @param cert 证书对象
     * @return 如果是OpenSSL生成的自签名证书返回true，否则返回false
     */
    private boolean isOpenSSLSelfSigned(X509Certificate cert) {
        if (cert == null) {
            return false;
        }

        // 检查是否为自签名
        if (!isSelfSigned(cert)) {
            return false;
        }

        // 获取subject字段
        Map<String, Object> subject = cert.getSubject();
        if (subject == null || subject.isEmpty()) {
            return false;
        }

        // 检查所有字段是否都在OPENSSL_FIELDS中
        for (String field : subject.keySet()) {
            String upperField = field.toUpperCase();
            if (!OPENSSL_FIELDS.contains(upperField)) {
                return false;
            }
        }

        return true;
    }

    /**
     * 检查证书是否为自签名
     */
    private boolean isSelfSigned(X509Certificate cert) {
        if (cert == null) {
            return false;
        }

        Map<String, Object> subject = cert.getSubject();
        Map<String, Object> issuer = cert.getIssuer();

        if (subject == null || issuer == null) {
            return false;
        }

        // 比较主题和颁发者的所有字段是否相同
        return subject.equals(issuer);
    }

    @Override
    protected void doDetect(X509Certificate cert) {
        if (isOpenSSLSelfSigned(cert)) {
            cert.getLabels().add(CertificateLabel.SELF_SIGNED_CERT);
            cert.getLabels().add(CertificateLabel.OPENSSL_SIGNED);
            log.debug("Detected OpenSSL self-signed certificate");
        }
    }
}
