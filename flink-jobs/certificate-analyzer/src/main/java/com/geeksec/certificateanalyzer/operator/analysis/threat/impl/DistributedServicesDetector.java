package com.geeksec.certificateanalyzer.operator.analysis.detector.impl;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import com.geeksec.certificateanalyzer.enums.CertificateLabel;
import com.geeksec.certificateanalyzer.model.cert.X509Certificate;
import com.geeksec.certificateanalyzer.operator.analysis.detector.BaseCertificateDetector;

import lombok.extern.slf4j.Slf4j;

/**
 * 分布式服务检测器
 * 检测证书是否包含多个具有相同根域名的子域名，用于识别分布式服务证书
 *
 * <AUTHOR>
 * @since 3.0.0
 */
@Slf4j
public class DistributedServicesDetector extends BaseCertificateDetector {
    
    public DistributedServicesDetector() {
        super("Distributed Services Detector");
    }
    
    @Override
    protected void doDetect(X509Certificate cert) {
        if (cert == null) {
            return;
        }
        
        List<String> domains = cert.getCertificateDomains();
        
        // 检测分布式服务
        if (hasCommonSubdomain(domains)) {
            cert.getLabels().add(CertificateLabel.DISTRIBUTED_SERVICES_CERT);
            log.debug("检测到分布式服务证书: {}", cert.getCommonName());
        }
    }
    
    /**
     * 检测是否有通用子域名
     * 用于识别分布式服务证书
     */
    private boolean hasCommonSubdomain(List<String> domains) {
        if (domains.size() < 2) {
            return false;
        }
        
        // 提取根域名
        Map<String, Integer> rootDomainCount = new HashMap<>();
        for (String domain : domains) {
            if (domain.startsWith("*.")) {
                domain = domain.substring(2);
            }
            
            // 简单的根域名提取逻辑
            String[] parts = domain.split("\\.");
            if (parts.length >= 2) {
                String rootDomain = parts[parts.length - 2] + "." + parts[parts.length - 1];
                rootDomainCount.put(rootDomain, rootDomainCount.getOrDefault(rootDomain, 0) + 1);
            }
        }
        
        // 如果有根域名出现多次，认为是分布式服务
        return rootDomainCount.values().stream().anyMatch(count -> count > 1);
    }
}
