package com.geeksec.certificateanalyzer.operator.analysis.scoring;

import com.geeksec.certificateanalyzer.model.cert.X509Certificate;
import com.geeksec.certificateanalyzer.enums.CertificateLabel;
import org.apache.flink.api.common.functions.MapFunction;
import lombok.extern.slf4j.Slf4j;

import java.time.LocalDateTime;

/**
 * 证书风险评分器
 * 计算证书的负面评分和正面评分
 *
 * <AUTHOR>
 * @Date 2022/11/8
 * @Modified hufengkai - 重构为使用枚举评分，更新为负面/正面评分
 * @Date 2025/01/15
 */

@Slf4j
public class CertificateRiskScorer implements MapFunction<X509Certificate, X509Certificate> {



    @Override
    public X509Certificate map(X509Certificate cert) throws Exception {
        // 直接使用标签枚举计算评分
        cert.setNegativeScore(calculateNegativeScore(cert.getLabels()));
        cert.setPositiveScore(calculatePositiveScore(cert.getLabels()));
        cert.setImportTime(LocalDateTime.now());
        return cert;
    }

    /**
     * 计算负面评分（原威胁评分）
     *
     * @param labels 证书标签集合
     * @return 计算得出的负面评分
     */
    private int calculateNegativeScore(java.util.Set<CertificateLabel> labels) {
        int score = 0;

        for (CertificateLabel label : labels) {
            score += label.getNegativeScore();
        }

        // 超过100取100
        return Math.min(score, 100);
    }

    /**
     * 计算正面评分（原信任评分）
     *
     * @param labels 证书标签集合
     * @return 计算得出的正面评分
     */
    private int calculatePositiveScore(java.util.Set<CertificateLabel> labels) {
        int score = 0;

        for (CertificateLabel label : labels) {
            score += label.getPositiveScore();
        }

        // 超过100取100
        return Math.min(score, 100);
    }

    /**
     * 计算威胁评分（兼容性方法）
     * @deprecated 使用 calculateNegativeScore() 替代
     */
    @Deprecated
    private int calculateThreatScore(java.util.Set<CertificateLabel> labels) {
        return calculateNegativeScore(labels);
    }

    /**
     * 计算信任评分（兼容性方法）
     * @deprecated 使用 calculatePositiveScore() 替代
     */
    @Deprecated
    private int calculateTrustScore(java.util.Set<CertificateLabel> labels) {
        return calculatePositiveScore(labels);
    }
}
