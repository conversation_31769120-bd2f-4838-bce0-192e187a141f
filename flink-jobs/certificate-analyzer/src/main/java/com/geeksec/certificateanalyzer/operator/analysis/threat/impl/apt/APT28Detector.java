package com.geeksec.certificateanalyzer.operator.analysis.detector.impl.apt;

import com.geeksec.certificateanalyzer.enums.CertificateLabel;
import com.geeksec.certificateanalyzer.model.cert.X509Certificate;
import com.geeksec.certificateanalyzer.operator.analysis.detector.BaseCertificateDetector;
import lombok.extern.slf4j.Slf4j;

import java.util.Set;

/**
 * APT28 威胁组织证书检测器
 * <p>
 * 检测 APT28 威胁组织使用的证书特征：
 * - 免费证书（FREE_CERTIFICATE）
 * - 最近注册证书（RECENTLY_REGISTERED）
 * - 非热门TLD证书（UNHOT_TLD）
 *
 * 检测逻辑：
 * 当证书同时满足以上三个条件时，判定为 APT28 威胁证书
 *
 * <AUTHOR>
 * @since 3.0.0
 */
@Slf4j
public class APT28Detector extends BaseCertificateDetector {
    public static final String DETECTOR_NAME = "APT28 Detector";
    
    public APT28Detector() {
        super(DETECTOR_NAME);
    }
    
    @Override
    protected void doDetect(X509Certificate cert) {
        if (cert == null) {
            return;
        }

        Set<CertificateLabel> labels = cert.getLabels();
        if (isApt28Certificate(cert)) {
            labels.add(CertificateLabel.APT28_CERT);
            log.debug("检测到潜在的 APT28 威胁证书: {}", cert.getCommonName());
        }
    }

    /**
     * 检查证书是否符合APT28的特征
     * <p>
     * 检测条件：
     * 1. 免费证书 (FREE_CERTIFICATE)
     * 2. 最近注册的证书 (RECENTLY_REGISTERED)
     * 3. 非常见顶级域名 (UNHOT_TLD)
     *
     * @param cert 证书对象
     * @return 如果符合 APT28 特征返回 true，否则返回 false
     */
    private boolean isApt28Certificate(X509Certificate cert) {
        Set<CertificateLabel> labels = cert.getLabels();

        boolean result = labels.contains(CertificateLabel.FREE_CERTIFICATE)
            && labels.contains(CertificateLabel.RECENTLY_REGISTERED)
            && labels.contains(CertificateLabel.UNHOT_TLD);

        if (result) {
            log.debug("APT28 检测条件满足: 免费证书 + 最近注册 + 非热门TLD");
        }

        return result;
    }
}
