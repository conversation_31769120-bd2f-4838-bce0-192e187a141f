package com.geeksec.certificateanalyzer.operator.analysis.detector.impl;

import java.util.Map;
import java.util.Set;

import com.geeksec.certificateanalyzer.config.CertificateConstants;
import com.geeksec.certificateanalyzer.enums.CertificateLabel;
import com.geeksec.certificateanalyzer.model.cert.X509Certificate;
import com.geeksec.certificateanalyzer.operator.analysis.detector.BaseCertificateDetector;
import com.geeksec.certificateanalyzer.util.cert.CertificateNameParser;
import com.geeksec.certificateanalyzer.util.validation.TorAddressValidator;

import lombok.extern.slf4j.Slf4j;

/**
 * Tor 相关证书检测器
 * 使用TorAddressValidator进行精确的Tor地址检测，支持V2和V3地址
 *
 * <AUTHOR>
 * @since 3.0.0
 */
@Slf4j
public class TorDetector extends BaseCertificateDetector {
    public static final String DETECTOR_NAME = "Tor Detector";

    public TorDetector() {
        super(DETECTOR_NAME);
    }

    @Override
    protected void doDetect(X509Certificate cert) {
        if (cert == null) {
            return;
        }

        Map<String, Object> subjectInfo = CertificateNameParser.parse(cert.getSubject());
        String subjectCN = subjectInfo.getOrDefault(CertificateConstants.FIELD_CN, "").toString();
        String checkResult = TorAddressValidator.validateTorAddress(subjectCN);

        Set<CertificateLabel> labels = cert.getLabels();

        if (TorAddressValidator.TOR_V3.equals(checkResult)) {
            labels.add(CertificateLabel.TOR_V3_CERT);
            labels.add(CertificateLabel.NETWORK_PENETRATION_CERT);
            log.debug("检测到Tor V3地址证书: {}", subjectCN);
        } else if (TorAddressValidator.TOR_V2.equals(checkResult)) {
            labels.add(CertificateLabel.TOR_V2_CERT);
            labels.add(CertificateLabel.NETWORK_PENETRATION_CERT);
            log.debug("检测到Tor V2地址证书: {}", subjectCN);
        }
    }
}
