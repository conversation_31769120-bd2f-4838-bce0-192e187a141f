package com.geeksec.certificateanalyzer.model.alarm;

import java.util.List;
import java.util.Map;

import lombok.Data;

/**
 * 告警事件模型
 * 用于证书分析器生成的告警事件，将发送到统一告警处理系统
 *
 * <AUTHOR>
 * @since 2025/07/16
 */
@Data
public class AlarmEvent {
    
    /**
     * 事件唯一标识
     */
    private String eventId;
    
    /**
     * 来源模块
     */
    private String sourceModule;
    
    /**
     * 告警类型
     */
    private String alarmType;
    
    /**
     * 告警名称
     */
    private String alarmName;
    
    /**
     * 威胁类型
     */
    private String threatType;
    
    /**
     * 告警级别：LOW, MEDIUM, HIGH, CRITICAL
     */
    private String alarmLevel;
    
    /**
     * 源IP地址
     */
    private String srcIp;
    
    /**
     * 目标IP地址
     */
    private String dstIp;
    
    /**
     * 源端口
     */
    private Integer srcPort;
    
    /**
     * 目标端口
     */
    private Integer dstPort;
    
    /**
     * 协议
     */
    private String protocol;
    
    /**
     * 时间戳
     */
    private String timestamp;
    
    /**
     * 检测器类型
     */
    private String detectorType;
    
    /**
     * 置信度 (0.0-1.0)
     */
    private Double confidence;
    
    /**
     * 描述
     */
    private String description;
    
    /**
     * 检测原因列表
     */
    private List<String> detectionReasons;
    
    /**
     * 标签列表
     */
    private List<String> labels;
    
    /**
     * 扩展属性
     */
    private Map<String, Object> extendedProperties;
    
    /**
     * 证书信息
     */
    private CertificateInfo certificateInfo;
    
    /**
     * 证书信息内部类
     */
    @Data
    public static class CertificateInfo {
        /**
         * 证书哈希
         */
        private String certHash;
        
        /**
         * 主题通用名称
         */
        private String subjectCn;
        
        /**
         * 颁发者通用名称
         */
        private String issuerCn;
        
        /**
         * 域名列表
         */
        private List<String> domains;
        
        /**
         * 生效时间
         */
        private String notBefore;
        
        /**
         * 过期时间
         */
        private String notAfter;
    }
}
