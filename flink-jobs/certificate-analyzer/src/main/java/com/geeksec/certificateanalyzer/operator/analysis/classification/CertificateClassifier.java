package com.geeksec.certificateanalyzer.operator.analysis.classification;

import com.geeksec.certificateanalyzer.model.cert.X509Certificate;
import com.geeksec.certificateanalyzer.enums.CertificateLabel;
import com.geeksec.certificateanalyzer.enums.BusinessCategory;
import com.geeksec.certificateanalyzer.enums.IndustryCategory;
import com.geeksec.certificateanalyzer.enums.UserCategory;
import lombok.extern.slf4j.Slf4j;

import java.util.*;

/**
 * 证书分类器
 * 从 threat-detector 模块迁移的证书分类功能
 * 对证书进行多维度分类和标签化
 *
 * <AUTHOR>
 * @since 3.0.0
 */
@Slf4j
public class CertificateClassifier {
    
    /**
     * 对证书进行分类并添加相应标签
     * 
     * @param certificate 证书对象
     */
    public void classifyCertificate(X509Certificate certificate) {
        if (certificate == null) {
            return;
        }
        
        log.debug("开始对证书进行分类: {}", certificate.getDerSha1());
        
        Set<CertificateLabel> labels = certificate.getLabels();
        if (labels == null) {
            labels = new HashSet<>();
        }
        
        // 确定主要分类
        String primaryCategory = determinePrimaryCategory(certificate);
        
        // 确定用途分类
        List<String> usageCategories = determineUsageCategories(certificate);
        
        // 确定行业分类
        String industryCategory = determineIndustryCategory(certificate);
        
        // 确定业务分类
        String businessCategory = determineBusinessCategory(certificate);
        
        // 确定用户分类
        String userCategory = determineUserCategory(certificate);
        
        // 添加分类标签
        addClassificationLabels(certificate, labels, primaryCategory, usageCategories, 
                               industryCategory, businessCategory, userCategory);
        
        certificate.setLabels(labels);
        
        log.debug("证书分类完成: {}, 主要分类: {}", certificate.getDerSha1(), primaryCategory);
    }
    
    /**
     * 确定主要分类
     */
    private String determinePrimaryCategory(X509Certificate certificate) {
        // 检查是否为系统证书
        if (isSystemCertificate(certificate)) {
            return "SYSTEM_CERTIFICATE";
        }
        
        // 检查是否为CA证书
        if (Boolean.TRUE.equals(certificate.getIsCACertificate())) {
            return "CA_CERTIFICATE";
        }
        
        // 检查是否为自签名证书
        if (Boolean.TRUE.equals(certificate.getIsSelfSigned())) {
            return "SELF_SIGNED_CERTIFICATE";
        }
        
        // 检查是否为可疑证书
        if (isSuspiciousCertificate(certificate)) {
            return "SUSPICIOUS_CERTIFICATE";
        }
        
        // 检查是否为企业证书
        if (isEnterpriseCertificate(certificate)) {
            return "ENTERPRISE_CERTIFICATE";
        }
        
        // 检查是否为Web服务器证书
        if (isWebServerCertificate(certificate)) {
            return "WEB_SERVER_CERTIFICATE";
        }
        
        // 默认为用户证书
        return "USER_CERTIFICATE";
    }
    
    /**
     * 确定用途分类
     */
    private List<String> determineUsageCategories(X509Certificate certificate) {
        List<String> categories = new ArrayList<>();
        
        // 基于扩展密钥用途确定
        List<String> extendedKeyUsage = certificate.getExtendedKeyUsage();
        if (extendedKeyUsage != null) {
            if (extendedKeyUsage.contains("serverAuth")) {
                categories.add("SERVER_AUTH");
            }
            if (extendedKeyUsage.contains("clientAuth")) {
                categories.add("CLIENT_AUTH");
            }
            if (extendedKeyUsage.contains("codeSigning")) {
                categories.add("CODE_SIGNING");
            }
            if (extendedKeyUsage.contains("emailProtection")) {
                categories.add("EMAIL_PROTECTION");
            }
        }
        
        // 基于CN确定用途
        String cn = getCommonName(certificate);
        if (cn != null) {
            if (cn.contains("@")) {
                categories.add("EMAIL");
            } else if (cn.startsWith("*.")) {
                categories.add("WILDCARD_DOMAIN");
            } else if (cn.matches("^[a-zA-Z0-9.-]+\\.[a-zA-Z]{2,}$")) {
                categories.add("DOMAIN");
            }
        }
        
        // 基于密钥用途确定
        List<String> keyUsage = certificate.getKeyUsage();
        if (keyUsage != null) {
            if (keyUsage.contains("digitalSignature")) {
                categories.add("DIGITAL_SIGNATURE");
            }
            if (keyUsage.contains("keyEncipherment")) {
                categories.add("ENCRYPTION");
            }
        }
        
        return categories.isEmpty() ? Arrays.asList("UNKNOWN") : categories;
    }
    
    /**
     * 确定行业分类
     */
    private String determineIndustryCategory(X509Certificate certificate) {
        String organization = getOrganization(certificate);
        String cn = getCommonName(certificate);
        
        if (organization != null || cn != null) {
            String text = (organization != null ? organization : "") + " " + (cn != null ? cn : "");
            text = text.toLowerCase();
            
            if (text.contains("bank") || text.contains("financial")) {
                return "FINANCIAL";
            }
            if (text.contains("hospital") || text.contains("medical") || text.contains("health")) {
                return "HEALTHCARE";
            }
            if (text.contains("gov") || text.contains("government")) {
                return "GOVERNMENT";
            }
            if (text.contains("edu") || text.contains("university") || text.contains("school")) {
                return "EDUCATION";
            }
            if (text.contains("tech") || text.contains("software") || text.contains("cloud")) {
                return "TECHNOLOGY";
            }
        }
        
        return "GENERAL";
    }
    
    /**
     * 确定业务分类
     */
    private String determineBusinessCategory(X509Certificate certificate) {
        // 基于证书类型和用途确定业务分类
        if (Boolean.TRUE.equals(certificate.getIsCACertificate())) {
            return "CA_BUSINESS";
        }
        
        List<String> extendedKeyUsage = certificate.getExtendedKeyUsage();
        if (extendedKeyUsage != null) {
            if (extendedKeyUsage.contains("serverAuth")) {
                return "WEB_SERVICE";
            }
            if (extendedKeyUsage.contains("codeSigning")) {
                return "SOFTWARE_DEVELOPMENT";
            }
            if (extendedKeyUsage.contains("emailProtection")) {
                return "EMAIL_SERVICE";
            }
        }
        
        return "GENERAL_BUSINESS";
    }
    
    /**
     * 确定用户分类
     */
    private String determineUserCategory(X509Certificate certificate) {
        if (isSystemCertificate(certificate)) {
            return "SYSTEM_USER";
        }
        
        if (isEnterpriseCertificate(certificate)) {
            return "ENTERPRISE_USER";
        }
        
        return "INDIVIDUAL_USER";
    }
    
    /**
     * 添加分类标签
     */
    private void addClassificationLabels(X509Certificate certificate, Set<CertificateLabel> labels,
                                       String primaryCategory, List<String> usageCategories,
                                       String industryCategory, String businessCategory, String userCategory) {
        
        // 根据主要分类添加标签
        switch (primaryCategory) {
            case "SYSTEM_CERTIFICATE":
                labels.add(CertificateLabel.SYSTEM_CERT);
                break;
            case "CA_CERTIFICATE":
                if (Boolean.TRUE.equals(certificate.getIsRootCA())) {
                    labels.add(CertificateLabel.ROOT_CA);
                } else {
                    labels.add(CertificateLabel.INTERMEDIATE_CA);
                }
                break;
            case "SELF_SIGNED_CERTIFICATE":
                labels.add(CertificateLabel.SELF_SIGNED_CERT);
                break;
            case "WEB_SERVER_CERTIFICATE":
                labels.add(CertificateLabel.SERVER_AUTH_CERT);
                break;
        }
        
        // 根据用途分类添加标签
        for (String usage : usageCategories) {
            switch (usage) {
                case "CODE_SIGNING":
                    labels.add(CertificateLabel.CODE_SIGNING_CERT);
                    break;
                case "EMAIL_PROTECTION":
                    labels.add(CertificateLabel.EMAIL_CERT);
                    break;
                case "SERVER_AUTH":
                    labels.add(CertificateLabel.SERVER_AUTH_CERT);
                    break;
                case "CLIENT_AUTH":
                    labels.add(CertificateLabel.CLIENT_AUTH_CERT);
                    break;
            }
        }
    }
    
    // 辅助方法
    private boolean isSystemCertificate(X509Certificate certificate) {
        // 检查是否为系统证书的逻辑
        return certificate.getLabels() != null && 
               certificate.getLabels().contains(CertificateLabel.SYSTEM_CERT);
    }
    
    private boolean isSuspiciousCertificate(X509Certificate certificate) {
        // 检查是否为可疑证书的逻辑
        Set<CertificateLabel> labels = certificate.getLabels();
        if (labels == null) return false;
        
        return labels.contains(CertificateLabel.FAKE_CERT) ||
               labels.contains(CertificateLabel.SUSPICIOUS_ORGANIZATION) ||
               labels.contains(CertificateLabel.SUSPICIOUS_ISSUER);
    }
    
    private boolean isEnterpriseCertificate(X509Certificate certificate) {
        String organization = getOrganization(certificate);
        return organization != null && !organization.trim().isEmpty();
    }
    
    private boolean isWebServerCertificate(X509Certificate certificate) {
        List<String> extendedKeyUsage = certificate.getExtendedKeyUsage();
        return extendedKeyUsage != null && extendedKeyUsage.contains("serverAuth");
    }
    
    private String getCommonName(X509Certificate certificate) {
        Map<String, Object> subject = certificate.getSubject();
        if (subject != null) {
            Object cn = subject.get("CN");
            return cn != null ? cn.toString() : null;
        }
        return null;
    }
    
    private String getOrganization(X509Certificate certificate) {
        Map<String, Object> subject = certificate.getSubject();
        if (subject != null) {
            Object o = subject.get("O");
            return o != null ? o.toString() : null;
        }
        return null;
    }
}
