package com.geeksec.certificateanalyzer.operator.analysis.trust;

import java.io.IOException;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

import org.apache.flink.api.common.functions.RichMapFunction;
import org.apache.flink.configuration.Configuration;

import com.geeksec.certificateanalyzer.enums.CertificateLabel;
import com.geeksec.certificateanalyzer.model.cert.X509Certificate;
import com.geeksec.certificateanalyzer.util.KnowledgeBaseUtils;
import com.geeksec.common.knowledge.KnowledgeBaseClient;

import lombok.extern.slf4j.Slf4j;

/**
 * 证书信任评估器
 * 
 * 职责：
 * 1. 评估证书的可信度和信任级别
 * 2. 检查证书的信任根和CA信誉
 * 3. 分析证书费用类型对信任度的影响
 * 4. 生成信任相关的标签
 * 
 * 评估内容包括：
 * - 信任根检查（Windows、CentOS、Apple等）
 * - CA信任度评估
 * - 证书费用类型评估（免费vs付费）
 * - 颁发者信誉评估
 * 
 * <AUTHOR>
 * @since 3.0.0
 */
@Slf4j
public class CertificateTrustEvaluator extends RichMapFunction<X509Certificate, X509Certificate> {

    /** 免费证书颁发机构列表 */
    private List<String> freeCertificateAuthorities = new ArrayList<>();

    /** 知识库客户端 */
    private KnowledgeBaseClient knowledgeBaseClient;

    /** EV证书策略OID列表 */
    private Set<String> evPolicyOids = new HashSet<>();

    /** DV证书策略OID列表 */
    private Set<String> dvPolicyOids = new HashSet<>();

    /** OV证书策略OID列表 */
    private Set<String> ovPolicyOids = new HashSet<>();

    @Override
    public void open(Configuration parameters) throws Exception {
        log.info("证书信任评估器初始化开始");

        // 创建知识库客户端实例
        knowledgeBaseClient = KnowledgeBaseUtils.createInstance(parameters);

        loadTrustEvaluationData();
    }

    @Override
    public void close() throws Exception {
        // 关闭知识库客户端
        if (knowledgeBaseClient != null) {
            try {
                knowledgeBaseClient.close();
            } catch (Exception e) {
                log.error("关闭知识库客户端失败: {}", e.getMessage(), e);
            }
        }
        super.close();
        log.info("证书信任评估器关闭");
    }

    @Override
    public X509Certificate map(X509Certificate certificate) throws Exception {
        log.debug("执行证书信任评估，证书ID: {}", certificate.getDerSha1());

        Set<CertificateLabel> labels = certificate.getLabels();
        if (labels == null) {
            labels = new HashSet<>();
        }

        // 信任根检查
        evaluateTrustedRoots(certificate, labels);

        // CA信任度评估
        evaluateCACredibility(certificate, labels);

        // 证书费用类型评估
        evaluateCertificateCostType(certificate, labels);

        // 颁发者信誉评估
        evaluateIssuerReputation(certificate, labels);

        // 证书验证类型评估（EV/DV/OV）
        evaluateCertificateValidationType(certificate, labels);

        certificate.setLabels(labels);
        return certificate;
    }

    /**
     * 加载信任评估所需的依赖数据
     */
    private void loadTrustEvaluationData() throws IOException {
        try {
            // 从知识库服务加载免费CA列表
            freeCertificateAuthorities = knowledgeBaseClient.getFreeCertificateAuthorities();
            log.info("成功从知识库加载免费CA列表 {} 个", freeCertificateAuthorities.size());

        } catch (Exception e) {
            log.error("从知识库加载信任评估数据失败，使用默认配置", e);
            // 使用默认的免费CA列表
            freeCertificateAuthorities = Arrays.asList(
                    "ZeroSSL.com", "zerossl",
                    "LetsEncrypt.org", "letsencrypt",
                    "LetsEncrypt.org_test", "letsencrypt_test", "letsencrypttest",
                    "BuyPass.com", "buypass",
                    "BuyPass.com_test", "buypass_test", "buypasstest",
                    "SSL.com", "sslcom",
                    "Google.com", "google",
                    "Google.com_test", "googletest", "google_test", "Cloudflare",
                    "SSL For Free", "WoTrus", "TrustAsia", "TrustAsia", "Microsoft Azure",
                    "GlobalSign SSL/TLS Certificates", "Comodo CA"
            );
        }
    }

    /**
     * 信任根检查
     * 检查证书是否在各操作系统的信任根列表中
     */
    private void evaluateTrustedRoots(X509Certificate certificate, Set<CertificateLabel> labels) {
        String certificateSha1 = certificate.getDerSha1();
        
        // Windows信任根检查
        List<String> windowsTrustedRoots = Arrays.asList(
                "06f1aa330b927b753a40e68cdf22e34bcbef3352",
                "31f9fc8ba3805986b721ea7295c65b3a44534274",
                "0119e81be9a14cd8e22f40ac118c687ecba3f4d8",
                "0563b8630d62d75abbc8ab1e4bdfb5a899b24d43"
        );

        if (certificateSha1 != null && windowsTrustedRoots.contains(certificateSha1.toLowerCase())) {
            labels.add(CertificateLabel.WINDOWS_TRUST);
            log.debug("检测到Windows信任根证书");
        }

        // 可以扩展其他操作系统的信任根检查
        // 例如：CentOS、Apple、Android、Firefox等
    }

    /**
     * CA信任度评估
     * 评估证书颁发机构的信誉和可信度
     */
    private void evaluateCACredibility(X509Certificate certificate, Set<CertificateLabel> labels) {
        // 这里可以根据CA的信誉评级进行评估
        // 例如：检查是否是知名的商业CA、政府CA等
        
        // 目前的实现可以基于知识库中的CA信誉数据
        // 后续可以扩展更复杂的信誉评估逻辑
    }

    /**
     * 证书费用类型评估
     * 分析证书是免费还是付费，这会影响信任度评估
     */
    private void evaluateCertificateCostType(X509Certificate certificate, Set<CertificateLabel> labels) {
        String issuerName = certificate.getIssuer().toString().toLowerCase();

        for (String freeCa : freeCertificateAuthorities) {
            if (issuerName.contains(freeCa.toLowerCase())) {
                labels.add(CertificateLabel.FREE_CERTIFICATE);
                log.debug("检测到免费证书，颁发机构: {}", freeCa);
                break;
            }
        }
        
        // 如果不是免费证书，可以标记为付费证书
        // 付费证书通常具有更高的信任度
    }

    /**
     * 颁发者信誉评估
     * 评估证书颁发者的信誉和可信度
     */
    private void evaluateIssuerReputation(X509Certificate certificate, Set<CertificateLabel> labels) {
        // 这里可以实现更复杂的颁发者信誉评估逻辑
        // 例如：
        // 1. 检查颁发者是否在黑名单中
        // 2. 检查颁发者的历史记录
        // 3. 检查颁发者是否有安全事件
        // 4. 评估颁发者的市场地位和声誉

        // 目前可以基于知识库中的数据进行基础评估
    }

    /**
     * 证书验证类型评估（EV/DV/OV）
     * 评估证书的验证级别
     */
    private void evaluateCertificateValidationType(X509Certificate certificate, Set<CertificateLabel> labels) {
        String issuerStr = certificate.getIssuer() != null ? certificate.getIssuer().toString().toLowerCase() : "";

        // 获取Subject信息
        Map<String, String> subject = certificate.getSubject();
        String subjectOu = "";
        if (subject != null) {
            subjectOu = subject.getOrDefault("OU", "");
        }

        // 获取证书策略
        String certificatePolicies = getExtensionValue(certificate, "certificatePolicies");
        Set<String> policyOids = extractPolicyOids(certificatePolicies);

        // EV证书检测
        if (issuerStr.contains("ev") || issuerStr.contains("extended validation") ||
                issuerStr.contains("serialnumber") || containsAnyOid(policyOids, evPolicyOids)) {
            labels.add(CertificateLabel.EV_CERT);
            log.debug("检测到EV证书");
        }
        // DV证书检测
        else if (issuerStr.contains("dv") || issuerStr.contains("domain validation") ||
                subjectOu.contains("Domain Control") || containsAnyOid(policyOids, dvPolicyOids)) {
            labels.add(CertificateLabel.DV_CERT);
            log.debug("检测到DV证书");
        }
        // OV证书检测
        else if (issuerStr.contains("ov") || issuerStr.contains("organization validation") ||
                containsAnyOid(policyOids, ovPolicyOids)) {
            labels.add(CertificateLabel.OV_CERT);
            log.debug("检测到OV证书");
        }
    }

    /**
     * 获取证书扩展值
     */
    private String getExtensionValue(X509Certificate certificate, String extensionName) {
        if (certificate.getCertificateExtensions() != null &&
                certificate.getCertificateExtensions().getExtensionMap() != null) {
            Object value = certificate.getCertificateExtensions().getExtensionMap().get(extensionName);
            return value != null ? value.toString() : "";
        }
        return "";
    }

    /**
     * 提取策略OID
     */
    private Set<String> extractPolicyOids(String certificatePolicies) {
        Set<String> oids = new HashSet<>();
        if (certificatePolicies != null && !certificatePolicies.isEmpty()) {
            String[] parts = certificatePolicies.split(",");
            for (String part : parts) {
                String trimmed = part.trim();
                if (trimmed.matches("\\d+(\\.\\d+)*")) {
                    oids.add(trimmed);
                }
            }
        }
        return oids;
    }

    /**
     * 检查是否包含任何目标OID
     */
    private boolean containsAnyOid(Set<String> policyOids, Set<String> targetOids) {
        for (String oid : policyOids) {
            if (targetOids.contains(oid)) {
                return true;
            }
        }
        return false;
    }
}
