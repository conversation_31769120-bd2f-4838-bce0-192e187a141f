package com.geeksec.certificateanalyzer.operator.analysis.detector.impl;

import java.util.List;

import com.geeksec.certificateanalyzer.enums.CertificateLabel;
import com.geeksec.certificateanalyzer.model.cert.X509Certificate;
import com.geeksec.certificateanalyzer.operator.analysis.detector.BaseCertificateDetector;
import com.geeksec.common.knowledge.KnowledgeBaseClient;

import lombok.extern.slf4j.Slf4j;

/**
 * 挖矿威胁检测器
 * 使用懒加载模式，通过KnowledgeBaseClient检测证书中的域名是否在挖矿威胁列表中
 *
 * <AUTHOR>
 * @since 3.0.0
 */
@Slf4j
public class MiningThreatDetector extends BaseCertificateDetector {

    /** 知识库客户端 */
    private final KnowledgeBaseClient knowledgeBaseClient;

    public MiningThreatDetector() {
        super("Mining Threat Detector");
        this.knowledgeBaseClient = new KnowledgeBaseClient();
        log.info("挖矿威胁检测器初始化完成，使用懒加载模式");
    }
    
    @Override
    protected void doDetect(X509Certificate cert) {
        if (cert == null) {
            return;
        }

        List<String> domains = cert.getCertDomains();

        // 懒加载检查挖矿域名
        for (String domain : domains) {
            if (knowledgeBaseClient.isMiningDomain(domain)) {
                cert.getLabels().add(CertificateLabel.MINING_CERT);
                log.debug("检测到挖矿威胁域名: {}", domain);
                break;
            }
        }
    }
}
