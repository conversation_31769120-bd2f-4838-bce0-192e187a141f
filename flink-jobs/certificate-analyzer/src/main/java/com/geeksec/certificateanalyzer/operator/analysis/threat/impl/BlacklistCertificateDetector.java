package com.geeksec.certificateanalyzer.operator.analysis.detector.impl;

import com.geeksec.certificateanalyzer.enums.CertificateLabel;
import com.geeksec.certificateanalyzer.model.cert.X509Certificate;
import com.geeksec.certificateanalyzer.operator.analysis.detector.BaseCertificateDetector;
import lombok.extern.slf4j.Slf4j;
import redis.clients.jedis.Jedis;
import redis.clients.jedis.JedisPool;

import java.util.Set;
import java.util.HashSet;

/**
 * 黑名单证书检测器
 * <p>
 * 检测证书是否在黑名单中，对应 NTA 2.0 中的黑名单证书检测逻辑：
 * - 检查证书 SHA1 是否在 Redis 黑名单缓存中
 * - 检查证书是否在知识库黑名单数据中
 * - 支持配置驱动的黑名单检测开关
 *
 * 黑名单证书特征：
 * - 已知的恶意证书
 * - 被吊销的证书
 * - 被标记为威胁的证书
 *
 * <AUTHOR>
 * @since 3.0.0
 */
@Slf4j
public class BlacklistCertificateDetector extends BaseCertificateDetector {

    /** Redis 连接池 */
    private final JedisPool jedisPool;

    /** 黑名单证书 SHA1 集合（本地缓存） */
    private Set<String> blacklistCertSha1Set = new HashSet<>();

    /** Redis 黑名单键前缀 */
    private static final String BLACKLIST_KEY_PREFIX = "cert:blacklist:";

    /** 黑名单配置键 */
    private static final String BLACKLIST_CONFIG_KEY = "cert:config:blacklist";

    /** 黑名单检测是否启用 */
    private boolean blacklistDetectionEnabled = true;

    public BlacklistCertificateDetector(JedisPool jedisPool) {
        super("Blacklist Certificate Detector");
        this.jedisPool = jedisPool;
        loadBlacklistData();
    }

    /**
     * 加载黑名单数据
     */
    private void loadBlacklistData() {
        if (jedisPool == null) {
            log.warn("Redis 连接池未初始化，使用空黑名单");
            return;
        }

        try (Jedis jedis = jedisPool.getResource()) {
            // 检查黑名单检测是否启用
            String configValue = jedis.get(BLACKLIST_CONFIG_KEY);
            if ("0".equals(configValue) || "false".equalsIgnoreCase(configValue)) {
                blacklistDetectionEnabled = false;
                log.info("黑名单证书检测已禁用");
                return;
            }

            // 加载黑名单证书 SHA1 列表
            Set<String> blacklistKeys = jedis.keys(BLACKLIST_KEY_PREFIX + "*");
            for (String key : blacklistKeys) {
                String certSha1 = key.substring(BLACKLIST_KEY_PREFIX.length());
                blacklistCertSha1Set.add(certSha1.toLowerCase());
            }

            log.info("成功加载黑名单证书数据: {} 个", blacklistCertSha1Set.size());

        } catch (Exception e) {
            log.error("加载黑名单证书数据失败", e);
            // 使用空集合作为默认值
            blacklistCertSha1Set = new HashSet<>();
        }
    }

    @Override
    protected void doDetect(X509Certificate cert) {
        if (cert == null || !blacklistDetectionEnabled) {
            return;
        }

        String sha1 = getCertificateSha1(cert);
        if (sha1 == null || sha1.isEmpty()) {
            return;
        }

        try {
            // 首先检查本地缓存
            if (isInLocalBlacklist(sha1)) {
                markAsBlacklisted(cert, sha1);
                return;
            }

            // 检查 Redis 黑名单
            if (isInRedisBlacklist(sha1)) {
                markAsBlacklisted(cert, sha1);
                // 更新本地缓存
                blacklistCertSha1Set.add(sha1.toLowerCase());
            }

        } catch (Exception e) {
            log.debug("黑名单证书检测过程中发生异常: {}", e.getMessage());
        }
    }

    /**
     * 获取证书的 SHA1 值
     */
    private String getCertificateSha1(X509Certificate cert) {
        String sha1 = cert.getCorrectedAsn1Sha1();
        if (sha1 == null || sha1.isEmpty()) {
            sha1 = cert.getDerSha1();
        }
        return sha1;
    }

    /**
     * 检查证书是否在本地黑名单缓存中
     */
    private boolean isInLocalBlacklist(String sha1) {
        return blacklistCertSha1Set.contains(sha1.toLowerCase());
    }

    /**
     * 检查证书是否在 Redis 黑名单中
     */
    private boolean isInRedisBlacklist(String sha1) {
        if (jedisPool == null) {
            return false;
        }

        try (Jedis jedis = jedisPool.getResource()) {
            String blacklistKey = BLACKLIST_KEY_PREFIX + sha1.toLowerCase();
            return jedis.exists(blacklistKey);
        } catch (Exception e) {
            log.debug("检查 Redis 黑名单失败: {}", e.getMessage());
            return false;
        }
    }

    /**
     * 标记证书为黑名单证书
     */
    private void markAsBlacklisted(X509Certificate cert, String sha1) {
        Set<CertificateLabel> labels = cert.getLabels();
        labels.add(CertificateLabel.BLOCKED);
        log.debug("检测到黑名单证书: {}", sha1);
    }

    /**
     * 刷新黑名单数据
     */
    public void refreshBlacklistData() {
        log.info("刷新黑名单证书数据");
        loadBlacklistData();
    }

    /**
     * 获取黑名单证书数量（用于监控）
     *
     * @return 黑名单证书数量
     */
    public int getBlacklistSize() {
        return blacklistCertSha1Set.size();
    }

    /**
     * 检查黑名单检测是否启用
     *
     * @return 如果启用返回 true，否则返回 false
     */
    public boolean isBlacklistDetectionEnabled() {
        return blacklistDetectionEnabled;
    }
}
