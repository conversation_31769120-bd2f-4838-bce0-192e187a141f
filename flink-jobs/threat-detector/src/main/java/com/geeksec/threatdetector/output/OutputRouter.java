package com.geeksec.threatdetector.output;

import com.geeksec.threatdetector.model.detection.DetectionResult;
import com.geeksec.threatdetector.model.output.AssetLabel;
import com.geeksec.threatdetector.model.output.SessionLabel;
import lombok.extern.slf4j.Slf4j;
import org.apache.flink.configuration.Configuration;
import org.apache.flink.streaming.api.functions.ProcessFunction;
import org.apache.flink.util.Collector;
import org.apache.flink.util.OutputTag;

/**
 * 输出路由器
 * 根据检测结果生成三种输出：会话标签、资产标签、通知消息
 * 告警事件输出已迁移到统一告警处理系统 (alarm-processor)
 *
 * <AUTHOR>
 */
@Slf4j
public class OutputRouter extends ProcessFunction<DetectionResult, DetectionResult> {

    private static final long serialVersionUID = 1L;

    // 三种输出标签（告警事件已迁移到统一告警处理系统）
    public static final OutputTag<SessionLabel> SESSION_LABEL_TAG =
            new OutputTag<SessionLabel>("session-labels") {};

    public static final OutputTag<AssetLabel> ASSET_LABEL_TAG =
            new OutputTag<AssetLabel>("asset-labels") {};

    public static final OutputTag<String> NOTIFICATION_TAG =
            new OutputTag<String>("notifications") {};

    @Override
    public void open(Configuration parameters) throws Exception {
        super.open(parameters);
        log.info("输出路由器初始化完成");
    }

    @Override
    public void processElement(DetectionResult result, Context ctx, Collector<DetectionResult> out) throws Exception {
        if (result == null) {
            return;
        }

        try {
            // 1. 生成会话标签
            SessionLabel sessionLabel = createSessionLabel(result);
            if (sessionLabel != null) {
                ctx.output(SESSION_LABEL_TAG, sessionLabel);
                log.debug("生成会话标签: 会话ID={}, 标签={}",
                        sessionLabel.getSessionId(), sessionLabel.getLabelValue());
            }

            // 2. 生成资产标签
            AssetLabel assetLabel = createAssetLabel(result);
            if (assetLabel != null) {
                ctx.output(ASSET_LABEL_TAG, assetLabel);
                log.debug("生成资产标签: 源IP={}, 目标IP={}, 标签={}",
                        assetLabel.getSrcIp(), assetLabel.getDstIp(), assetLabel.getLabelValue());
            }

            // 3. 告警事件生成已迁移到统一告警处理系统 (alarm-processor)
            log.debug("告警事件生成已迁移到统一告警处理系统");

            // 4. 生成通知消息（如果需要）
            String notification = createNotification(result);
            if (notification != null) {
                ctx.output(NOTIFICATION_TAG, notification);
                log.debug("生成通知消息: {}", notification);
            }

            // 将原始检测结果传递到主输出流
            out.collect(result);

        } catch (Exception e) {
            log.error("输出路由处理异常: {}", e.getMessage(), e);
            // 即使出现异常，也要传递原始结果
            out.collect(result);
        }
    }

    /**
     * 创建会话标签
     *
     * @param result 检测结果
     * @return 会话标签
     */
    private SessionLabel createSessionLabel(DetectionResult result) {
        return SessionLabel.builder()
                .sessionId(result.getSessionId())
                .srcIp(result.getSrcIp())
                .dstIp(result.getDstIp())
                .srcPort(result.getSrcPort())
                .dstPort(result.getDstPort())
                .protocol(result.getProtocol())
                .labelKey(result.getThreatType())
                .labelValue(result.getThreatName())
                .confidence(result.getConfidence())
                .detectorType(result.getDetectorType())
                .timestamp(result.getDetectionTime())
                .build();
    }

    /**
     * 创建资产标签
     *
     * @param result 检测结果
     * @return 资产标签
     */
    private AssetLabel createAssetLabel(DetectionResult result) {
        return AssetLabel.builder()
                .srcIp(result.getSrcIp())
                .dstIp(result.getDstIp())
                .labelId(generateLabelId(result))
                .labelKey(result.getThreatType())
                .labelValue(result.getThreatName())
                .confidence(result.getConfidence())
                .detectorType(result.getDetectorType())
                .timestamp(result.getDetectionTime())
                .build();
    }

    /**
     * 创建告警事件（已迁移到统一告警处理系统）
     *
     * @deprecated 告警事件创建已迁移到 alarm-processor 模块
     */
    @Deprecated
    private void createAlarmEvent(DetectionResult result) {
        // 告警事件创建已迁移到统一告警处理系统 (alarm-processor)
        // threat-detector 将检测结果发送到 Kafka，由 alarm-processor 统一处理
        log.debug("告警事件创建已迁移到统一告警处理系统");
    }

    /**
     * 创建通知消息
     *
     * @param result 检测结果
     * @return 通知消息（JSON格式）
     */
    private String createNotification(DetectionResult result) {
        // 只有高级别威胁才发送通知
        if (result.getThreatLevel().getLevel() < 3) {
            return null;
        }

        // 构建简单的JSON通知消息
        return String.format(
                "{\"type\":\"threat_alert\",\"threat_type\":\"%s\",\"threat_name\":\"%s\"," +
                "\"level\":\"%s\",\"src_ip\":\"%s\",\"dst_ip\":\"%s\",\"confidence\":%.2f," +
                "\"detector\":\"%s\",\"timestamp\":\"%s\"}",
                result.getThreatType(),
                result.getThreatName(),
                result.getThreatLevel().name(),
                result.getSrcIp(),
                result.getDstIp(),
                result.getConfidence(),
                result.getDetectorType().getDisplayName(),
                result.getDetectionTime()
        );
    }

    /**
     * 生成标签ID
     *
     * @param result 检测结果
     * @return 标签ID
     */
    private Long generateLabelId(DetectionResult result) {
        // 使用威胁类型的哈希值作为标签ID
        return (long) result.getThreatType().hashCode();
    }

    /**
     * 生成告警ID（已迁移到统一告警处理系统）
     *
     * @deprecated 告警ID生成已迁移到 alarm-processor 模块
     */
    @Deprecated
    private String generateAlarmId(DetectionResult result) {
        // 告警ID生成已迁移到统一告警处理系统 (alarm-processor)
        return "deprecated";
    }
}
