package com.geeksec.flink.sink;

import java.util.Properties;

import org.apache.flink.api.java.utils.ParameterTool;
import org.apache.flink.connector.kafka.sink.KafkaRecordSerializationSchema;
import org.apache.flink.connector.kafka.sink.KafkaSink;
import org.apache.flink.connector.base.DeliveryGuarantee;
import org.apache.kafka.clients.producer.ProducerRecord;

import com.fasterxml.jackson.databind.ObjectMapper;

import lombok.extern.slf4j.Slf4j;

/**
 * 告警事件 Kafka Sink 工厂
 * 用于创建标准化的告警事件 Kafka 输出
 *
 * <AUTHOR>
 * @since 2025/07/16
 */
@Slf4j
public class AlarmEventKafkaSink {

    private static final ObjectMapper OBJECT_MAPPER = new ObjectMapper();

    /**
     * 创建告警事件 Kafka Sink
     *
     * @param config 配置参数
     * @param <T> 告警事件类型
     * @return Kafka Sink
     */
    public static <T> KafkaSink<T> create(ParameterTool config) {
        // 获取配置参数
        String bootstrapServers = config.get("kafka.bootstrap.servers", "localhost:9092");
        String alarmTopic = config.get("kafka.alarm.topic", "alarm-events");
        
        log.info("创建告警事件 Kafka Sink: servers={}, topic={}", bootstrapServers, alarmTopic);

        // 创建序列化器
        KafkaRecordSerializationSchema<T> serializer = createAlarmEventSerializer(alarmTopic);

        // 创建生产者配置
        Properties producerConfig = createProducerConfig();

        // 构建 Kafka Sink
        return KafkaSink.<T>builder()
                .setBootstrapServers(bootstrapServers)
                .setRecordSerializer(serializer)
                .setKafkaProducerConfig(producerConfig)
                .setDeliveryGuarantee(DeliveryGuarantee.AT_LEAST_ONCE)
                .build();
    }

    /**
     * 创建告警事件序列化器
     */
    private static <T> KafkaRecordSerializationSchema<T> createAlarmEventSerializer(String topic) {
        return new KafkaRecordSerializationSchema<T>() {
            @Override
            public ProducerRecord<byte[], byte[]> serialize(T alarmEvent, 
                                                          KafkaSinkContext context, 
                                                          Long timestamp) {
                try {
                    // 将告警事件序列化为 JSON
                    String jsonString = OBJECT_MAPPER.writeValueAsString(alarmEvent);
                    byte[] value = jsonString.getBytes("UTF-8");
                    
                    // 使用时间戳作为 key
                    String key = String.valueOf(System.currentTimeMillis());
                    byte[] keyBytes = key.getBytes("UTF-8");
                    
                    return new ProducerRecord<>(topic, keyBytes, value);
                    
                } catch (Exception e) {
                    log.error("告警事件序列化失败", e);
                    // 返回空记录，避免中断流处理
                    return new ProducerRecord<>(topic, null, "{}".getBytes());
                }
            }
        };
    }

    /**
     * 创建生产者配置
     */
    private static Properties createProducerConfig() {
        Properties props = new Properties();
        
        // 基础配置
        props.put("acks", "1");
        props.put("retries", 3);
        props.put("batch.size", 16384);
        props.put("linger.ms", 1000);
        props.put("buffer.memory", 33554432);
        
        // 序列化配置
        props.put("key.serializer", "org.apache.kafka.common.serialization.ByteArraySerializer");
        props.put("value.serializer", "org.apache.kafka.common.serialization.ByteArraySerializer");
        
        // 超时配置
        props.put("request.timeout.ms", "30000");
        props.put("delivery.timeout.ms", "120000");
        
        // 压缩配置
        props.put("compression.type", "snappy");
        
        return props;
    }

    /**
     * 创建带自定义配置的告警事件 Kafka Sink
     *
     * @param bootstrapServers Kafka 服务器地址
     * @param topic 告警事件主题
     * @param <T> 告警事件类型
     * @return Kafka Sink
     */
    public static <T> KafkaSink<T> create(String bootstrapServers, String topic) {
        log.info("创建告警事件 Kafka Sink: servers={}, topic={}", bootstrapServers, topic);

        KafkaRecordSerializationSchema<T> serializer = createAlarmEventSerializer(topic);
        Properties producerConfig = createProducerConfig();

        return KafkaSink.<T>builder()
                .setBootstrapServers(bootstrapServers)
                .setRecordSerializer(serializer)
                .setKafkaProducerConfig(producerConfig)
                .setDeliveryGuarantee(DeliveryGuarantee.AT_LEAST_ONCE)
                .build();
    }

    /**
     * 创建带完整配置的告警事件 Kafka Sink
     *
     * @param bootstrapServers Kafka 服务器地址
     * @param topic 告警事件主题
     * @param producerConfig 自定义生产者配置
     * @param <T> 告警事件类型
     * @return Kafka Sink
     */
    public static <T> KafkaSink<T> create(String bootstrapServers, 
                                         String topic, 
                                         Properties producerConfig) {
        log.info("创建告警事件 Kafka Sink: servers={}, topic={}", bootstrapServers, topic);

        KafkaRecordSerializationSchema<T> serializer = createAlarmEventSerializer(topic);

        return KafkaSink.<T>builder()
                .setBootstrapServers(bootstrapServers)
                .setRecordSerializer(serializer)
                .setKafkaProducerConfig(producerConfig)
                .setDeliveryGuarantee(DeliveryGuarantee.AT_LEAST_ONCE)
                .build();
    }
}
